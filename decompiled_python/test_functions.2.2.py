# Source Generated with <PERSON><PERSON><PERSON>le++
# File: test_functions.2.2.pyc (Python 2.2)


def x0():
    pass


def x1(arg1):
    pass


def x2(arg1, arg2):
    pass


def x3a(*args):
    pass


def x3b(**kwargs):
    pass


def x3c(*args, **kwargs):
    pass


def x4a(foo, bar = 1, bla = 2, *args):
    pass


def x4b(foo, bar = 1, bla = 2, **kwargs):
    pass


def x4c(foo, bar = 1, bla = 2, *args, **kwargs):
    pass


def func_with_tuple_args(.0):
    (a, b) = .0
    print a
    print b


def func_with_tuple_args2(.0, .2):
    (a, b) = .0
    (c, d) = .2
    print a
    print c


def func_with_tuple_args3(.0, .2, *args):
    (a, b) = .0
    (c, d) = .2
    print a
    print c


def func_with_tuple_args4(.0, .2, **kwargs):
    (a, b) = .0
    (c, d) = .2
    print a
    print c


def func_with_tuple_args5(.0, .2, *args, **kwargs):
    (a, b) = .0
    (c, d) = .2
    print a
    print c


def func_with_tuple_args6(.0, .2 = (2, 3), *args, **kwargs):
    (a, b) = .0
    (c, d) = .2
    print a
    print c

