# Source Generated with Decom<PERSON>le++
# File: test_docstring.2.7.pyc (Python 2.7)

'''
Module docstring
'''

def Doc_Test():
    '''Function docstring'''
    pass


class XXX:
    '''Class docstring'''
    
    def __init__(self):
        '''__init__:  Member function docstring'''
        self.a = 1
        
        def XXX22():
            '''XXX22: Nested function docstring'''
            pass


    
    def XXX11():
        '''XXX11: Member Function docstring'''
        pass

    
    def XXX12():
        foo = 'XXX12: Normal string'

    
    def XXX13():
        pass



def Y11():
    
    def Y22():
        
        def Y33():
            pass



print __doc__
