# Source Generated with Decompyle++
# File: remote.pyc (Python 3.12)

import asyncio
import contextlib
import json
import logging
import os
import re
import shutil
import ssl
from collections import deque
from collections.abc import AsyncIterator, Callable
from typing import Any, Literal, Self, cast
import certifi
import httpx
import orjson
import structlog
import uvicorn
from langchain_core.runnables.config import RunnableConfig
from langchain_core.runnables.graph import Edge, Node
from langchain_core.runnables.graph import Graph as DrawableGraph
from langchain_core.runnables.schema import CustomStreamEvent, StandardStreamEvent, StreamEvent
from langgraph.checkpoint.serde.base import SerializerProtocol
from langgraph.store.base import GetOp, Item, ListNamespacesOp, PutOp, SearchOp
from langgraph.types import Command, PregelTask, Send, StateSnapshot
from langgraph_sdk import Auth
from pydantic import BaseModel
from starlette import types
from starlette.applications import Starlette
from starlette.authentication import AuthCredentials, AuthenticationBackend, BaseUser
from starlette.datastructures import MutableHeaders
from starlette.exceptions import HTTPException
from starlette.requests import HTTPConnection, Request
from starlette.routing import Route
from langgraph_api import store as api_store
from langgraph_api.auth.custom import DotDict, ProxyUser
from langgraph_api.config import LANGGRAPH_AUTH, LANGGRAPH_AUTH_TYPE
from langgraph_api.js.base import BaseRemotePregel, RemoteInterrupt
from langgraph_api.js.errors import RemoteException
from langgraph_api.js.sse import SSEDecoder, aiter_lines_raw
from langgraph_api.route import ApiResponse
from langgraph_api.schema import Config
from langgraph_api.serde import json_dumpb
logger = structlog.stdlib.get_logger(__name__)
REMOTE_PORT = 5555
GRAPH_PORT = 5556
GRAPH_HTTP_PORT = 5557
SSL = ssl.create_default_context(cafile = certifi.where())
port = int(os.getenv('PORT', '8080'))
if int(os.getenv('PORT', '8080')) and port in (GRAPH_PORT, REMOTE_PORT):
    raise ValueError(f'''PORT={port} is a reserved port for the JS worker. Please choose a different port.''')
_client = httpx.AsyncClient(base_url = f'''http://localhost:{GRAPH_PORT}''', timeout = httpx.Timeout(15), limits = httpx.Limits(), transport = httpx.AsyncHTTPTransport(verify = SSL))

def _snapshot_defaults():
    if not hasattr(StateSnapshot, 'interrupts'):
        return { }
    return {
        None: tuple() }


def default_command(obj):
    if isinstance(obj, Send):
        return {
            'node': obj.node,
            'args': obj.arg }
    if None(obj, ProxyUser):
        return obj.dict()
    raise None


def _client_stream(method = None, data = None):
    pass
# WARNING: Decompyle incomplete


class NoopModel(BaseModel):
    pass


async def _client_invoke(method = None, data = None):
    pass
# WARNING: Decompyle incomplete


class RemotePregel(BaseRemotePregel):
    pass
# WARNING: Decompyle incomplete


async def run_js_process(paths_str = None, watch = None):
    pass
# WARNING: Decompyle incomplete


async def run_js_http_process(paths_str = None, http_config = None, watch = None):
    pass
# WARNING: Decompyle incomplete

_BAD_SURROGATE_RE = re.compile('\\\\u[dD][89a-fA-F][0-9a-fA-F]{2}')
_BAD_ESCAPE_RE = re.compile('\\\\(?![\\"\\\\/bfnrtu])')

def _safe_json_loads(data = None):
    '''Attempt *orjson.loads* first; if it fails, repair common escape issues.

    For a time, we had a bug in our surrogate cleanup in serde.py, which
    allowed sequences containing a stray backslash to be stored which would
    then fail upon loading. This function attempts to repair those sequences.
    '''
    return orjson.loads(data)
# WARNING: Decompyle incomplete


class PassthroughSerialiser(SerializerProtocol):
    
    def dumps(self = None, obj = None):
        return json_dumpb(obj)

    
    def dumps_typed(self = None, obj = None):
        return ('json', json_dumpb(obj))

    
    def loads(self = None, data = None):
        return _safe_json_loads(data)

    
    def loads_typed(self = None, data = None):
        (type, payload) = data
        if type != 'json':
            raise ValueError(f'''Unsupported type {type}''')
        return _safe_json_loads(payload)



def _get_passthrough_checkpointer():
    Checkpointer = Checkpointer
    import langgraph_runtime.checkpoint
    checkpointer = Checkpointer()
    checkpointer.serde = PassthroughSerialiser()
    return checkpointer


async def _get_passthrough_store():
    pass
# WARNING: Decompyle incomplete


async def run_remote_checkpointer():
    pass
# WARNING: Decompyle incomplete


class DisableHttpxLoggingContextManager(contextlib.AbstractContextManager):
    filter: logging.Filter = '\n    Disable HTTP/1.1 200 OK logs spamming stdout.\n    '
    
    def __init__(self = None, filter = None):
        if not filter:
            filter
        
        self.filter = lambda record: '200 OK' not in record.getMessage()

    
    def __enter__(self):
        logging.getLogger('httpx').addFilter(self.filter)

    
    def __exit__(self, exc_type, exc_value, traceback):
        logging.getLogger('httpx').removeFilter(self.filter)



async def wait_until_js_ready():
    pass
# WARNING: Decompyle incomplete


async def js_healthcheck():
    pass
# WARNING: Decompyle incomplete


class CustomJsAuthBackend(AuthenticationBackend):
    ls_auth: AuthenticationBackend | None = 'CustomJsAuthBackend'
    
    def __init__(self = None, disable_studio_auth = None):
        LRUCache = LRUCache
        import langgraph_api.utils.cache
        self.ls_auth = None
        if disable_studio_auth and LANGGRAPH_AUTH_TYPE == 'langsmith':
            LangsmithAuthBackend = LangsmithAuthBackend
            import langgraph_api.auth.langsmith.backend
            self.ls_auth = LangsmithAuthBackend()
        self.ttl_cache = None
        self.cache_keys = None
        cache = LANGGRAPH_AUTH.get('cache')
    # WARNING: Decompyle incomplete

    
    async def authenticate(self = None, conn = None):
        pass
    # WARNING: Decompyle incomplete



async def handle_js_auth_event(ctx = None, value = None):
    pass
# WARNING: Decompyle incomplete


class JSCustomHTTPProxyMiddleware:
    
    def __init__(self = None, app = None):
        self.app = app
        self.proxy_client = httpx.AsyncClient(base_url = f'''http://localhost:{GRAPH_HTTP_PORT}''', timeout = httpx.Timeout(None), limits = httpx.Limits(), transport = httpx.AsyncHTTPTransport(verify = SSL))

    
    async def __call__(self = None, scope = None, receive = None, send = ('scope', types.Scope, 'receive', types.Receive, 'send', types.Send, 'return', None)):
        pass
    # WARNING: Decompyle incomplete

    
    async def __aenter__(self):
        pass
    # WARNING: Decompyle incomplete

    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
    # WARNING: Decompyle incomplete


