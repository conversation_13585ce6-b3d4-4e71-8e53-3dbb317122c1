# Source Generated with Decom<PERSON><PERSON>++
# File: test_functions_py3.3.0.pyc (Python 3.0)


def x0():
    pass


def x1(arg1):
    pass


def x2(arg1, arg2):
    pass


def x3a(*args):
    pass


def x3b(**kwargs):
    pass


def x3c(*args, **kwargs):
    pass


def x4a(foo, bar = 1, bla = 2, *args):
    pass


def x4b(foo, bar = 1, bla = 2, **kwargs):
    pass


def x4c(foo, bar = 1, bla = 2, *args, **kwargs):
    pass


def x5a(*, bar = 1):
    pass


def x5b(*, bar = 1, **kwargs):
    pass


def x6a(foo, *, bar = 1):
    pass


def x7a(foo, *, bar = 1, **kwargs):
    pass

