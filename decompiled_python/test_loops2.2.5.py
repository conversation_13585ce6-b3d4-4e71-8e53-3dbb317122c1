# Source Generated with Decompyle++
# File: test_loops2.2.5.pyc (Python 2.5)

'''
test_loops2.py -- source test pattern for loops (CONTINUE_LOOP)

This source is part of the decompyle test suite.

decompyle is a Python byte-code decompiler
See http://www.goebel-consult.de/decompyle/ for download and
for further information
'''
for term in args:
    
    try:
        print 
        continue
        print 
    continue
    continue


