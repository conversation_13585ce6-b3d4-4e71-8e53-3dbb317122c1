# Source Generated with <PERSON><PERSON><PERSON><PERSON>++
# File: test_functions_py3.3.7.pyc (Python 3.7)


def x0():
    pass


def x1(arg1):
    pass


def x2(arg1, arg2):
    pass


def x3a(*args):
    pass


def x3b(**kwargs):
    pass


def x3c(*args, **kwargs):
    pass


def x4a(foo, bar, bla = (1, 2), *args):
    pass


def x4b(foo, bar, bla = (1, 2), **kwargs):
    pass


def x4c(foo, bar, bla = (1, 2), *args, **kwargs):
    pass


def x5a(*, bar):
    pass


def x5b(*, bar):
    pass


def x5c(*, bar, **kwargs):
    pass


def x6a(foo, *, bar):
    pass


def x6b(foo = None, *, bar):
    pass


def x6c(foo = (1,), *, bar):
    pass


def x6d(foo = None, *, bar):
    pass


def x7a(foo, *, bar, **kwargs):
    pass


def x7b(foo = None, *, bar, **kwargs):
    pass


def x7c(foo = (1,), *, bar, **kwargs):
    pass


def x7d(foo = None, *, bar, **kwargs):
    pass

