# Source Generated with Decom<PERSON>le++
# File: sse.pyc (Python 3.12)

'''Adapted from httpx_sse to split lines on 
, \r, \r
 per the SSE spec.'''
import contextlib
from collections.abc import AsyncIterator
import httpx
import orjson
from langgraph_sdk.schema import StreamPart
BytesLike = bytes | bytearray | memoryview

class BytesLineDecoder:
    '''
    <PERSON><PERSON> incrementally reading lines from text.

    Has the same behaviour as the stdllib bytes splitlines,
    but handling the input iteratively.
    '''
    
    def __init__(self = None):
        self.buffer = bytearray()
        self.trailing_cr = False

    
    def decode(self = None, text = None):
        NEWLINE_CHARS = b'\n\r'
        if self.trailing_cr:
            text = b'\r' + text
            self.trailing_cr = False
        if text.endswith(b'\r'):
            self.trailing_cr = True
            text = text[:-1]
        if not text:
            return []
        trailing_newline = None[-1] in NEWLINE_CHARS
        lines = text.splitlines()
        if not len(lines) == 1 and trailing_newline:
            self.buffer.extend(lines[0])
            return []
        if None.buffer:
            self.buffer.extend(lines[0])
            lines = [
                self.buffer] + lines[1:]
            self.buffer = bytearray()
        if not trailing_newline:
            self.buffer.extend(lines.pop())
        return lines

    
    def flush(self = None):
        if not self.buffer and self.trailing_cr:
            return []
        lines = [
            None.buffer]
        self.buffer = bytearray()
        self.trailing_cr = False
        return lines



class SSEDecoder:
    
    def __init__(self = None):
        self._event = ''
        self._data = bytearray()
        self._last_event_id = ''
        self._retry = None

    
    def decode(self = None, line = None):
        pass
    # WARNING: Decompyle incomplete



def aiter_lines_raw(response = None):
    pass
# WARNING: Decompyle incomplete

