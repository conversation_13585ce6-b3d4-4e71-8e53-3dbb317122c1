# Source Generated with <PERSON><PERSON><PERSON><PERSON>++
# File: ui.pyc (Python 3.12)

import asyncio
import contextlib
import os
import shutil
import sys
from pathlib import Path
import structlog
from langgraph_api.config import UI_USE_BUNDLER
logger = structlog.stdlib.get_logger(__name__)
bg_tasks: set[asyncio.Task] = set()
UI_ROOT_DIR = Path(os.path.abspath('.langgraph_api') if UI_USE_BUNDLER else os.path.dirname(__file__)) / 'ui'
UI_PUBLIC_DIR = UI_ROOT_DIR / 'public'
UI_SCHEMAS_FILE = UI_ROOT_DIR / 'schemas.json'

async def start_ui_bundler():
    pass
# WARNING: Decompyle incomplete


async def stop_ui_bundler():
    pass
# WARNING: Decompyle incomplete


async def _start_ui_bundler_process():
    pass
# WARNING: Decompyle incomplete


def _handle_exception(task = None):
    task.result()
    bg_tasks.discard(task)
    sys.exit(1)
    return None
# WARNING: Decompyle incomplete

