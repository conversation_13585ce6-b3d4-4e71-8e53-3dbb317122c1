# Source Generated with <PERSON><PERSON><PERSON><PERSON>++
# File: test_nested_elif.1.5.pyc (Python 1.5)

a = None
if a == 1:
    print '1'
elif a == 2:
    print '2'

if a == 1:
    print '1'
elif a == 2:
    print '2'
else:
    print 'other'
if a == 1:
    print '1'
elif a == 2:
    print '2'
elif a == 3:
    print '3'
else:
    print 'other'
if a == 1:
    print '1'
elif a == 2:
    print '2'
elif a == 3:
    print '3'

if a == 1:
    print '1'
elif a == 2:
    print '2'
elif a == 3:
    print '3'
else:
    print 'other'
if a == 1:
    print '1'
elif a == 2:
    print '2'
else:
    print 'more'
    if a == 3:
        print '3'
    else:
        print 'other'
if a == 1:
    print '1'
else:
    print 'more'
    if a == 2:
        print '2'
    elif a == 3:
        print '3'
    else:
        print 'other'
if a == 1:
    print '1'
else:
    print 'more'
    if a == 2:
        print '2'
    else:
        print 'more'
        if a == 3:
            print '3'
        elif a == 4:
            print '4'
        elif a == 4:
            print '4'
        else:
            print 'other'
