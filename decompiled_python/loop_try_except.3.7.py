# Source Generated with <PERSON>om<PERSON>le++
# File: loop_try_except.3.7.pyc (Python 3.7)


async def myFunc():
    async for b in c:
        
        try:
            STUFF
        continue
        except MyException:
            running = False
            continue
        


for b in c:
    stuff
    
    try:
        STUFF
    continue
    except MyException:
        running = False
        continue
    


while a:
    stuff
    
    try:
        STUFF
    continue
    except MyException:
        running = False
        continue
    

