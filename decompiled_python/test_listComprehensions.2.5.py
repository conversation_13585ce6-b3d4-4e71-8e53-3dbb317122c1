# Source Generated with Decompyle++
# File: test_listComprehensions.2.5.pyc (Python 2.5)

XXX = range(4)
print [ i for i in XXX ]
print 
print [ i for i in (1, 2, 3, 4) ]
print 
print [ (i, 1) for i in XXX ]
print 
print [ i * 2 for i in range(4) ]
print 
print [ i * j for i in range(4) for j in range(7) ]
print _[6]
print _[7]
print []
seq1 = 'abc'
seq2 = (1, 2, 3)
[ (x, y) for x in seq1 for y in seq2 ]

def flatten(seq):
    return [ x for subseq in seq for x in subseq ]

print flatten([
    [
        0],
    [
        1,
        2,
        3],
    [
        4,
        5],
    [
        6,
        7,
        8,
        9],
    []])
