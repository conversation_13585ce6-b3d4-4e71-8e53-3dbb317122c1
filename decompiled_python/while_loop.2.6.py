# Source Generated with <PERSON><PERSON><PERSON>le++
# File: while_loop.2.6.pyc (Python 2.6)


class Test2:
    
    def count(self):
        a = [
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            10]
        i = 0
        while i < len(a):
            print a[i]
            i += 1
        print 'done'


