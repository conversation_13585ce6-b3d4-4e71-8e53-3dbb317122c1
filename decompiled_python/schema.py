# Source Generated with Decompyle++
# File: schema.pyc (Python 3.12)

from typing import Any
from typing_extensions import TypedDict

class RequestPayload(TypedDict):
    data: dict = 'RequestPayload'


class ResponsePayload(TypedDict):
    data: Any | None = 'ResponsePayload'


class StreamPingData(TypedDict):
    id: str = 'StreamPingData'


class StreamData(TypedDict):
    value: Any = 'StreamData'


class ErrorData(TypedDict):
    message: str = 'ErrorData'

