# Source Generated with Decompyle++
# File: test_import.2.5.pyc (Python 2.5)

'''
test_import.py -- source test pattern for import statements

This source is part of the decompyle test suite.

decompyle is a Python byte-code decompiler
See http://www.goebel-consult.de/decompyle/ for download and
for further information
'''
import sys
import os
import sys
import BaseHTTPServer
import test.test_MimeWriter as test
from rfc822 import Message
from mimetools import Message, decode, choose_boundary
from os import *
for k, v in globals().items():
    print `k`, v

