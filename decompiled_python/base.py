# Source Generated with Decompyle++
# File: base.pyc (Python 3.12)

import os
from collections.abc import Sequence
from dataclasses import dataclass
from typing import Any, Literal
from langchain_core.runnables import Runnable
from langgraph_api.schema import Config
_DC_KWARGS = {
    'kw_only': True,
    'slots': True,
    'frozen': True }
JS_EXTENSIONS = ('.ts', '.mts', '.cts', '.js', '.mjs', '.cjs')

def is_js_path(path = None):
    pass
# WARNING: Decompyle incomplete

# WARNING: Decompyle incomplete
