import { StateGraph } from "@langchain/langgraph";

// 定义图状态接口
interface ChatState {
  messages: string[];
  user_input?: string;
  response?: string;
}

// 创建状态图
const graph = new StateGraph<ChatState>({
  channels: {
    messages: {
      value: (x: string[], y: string[]) => x.concat(y),
      default: () => [],
    },
    user_input: {
      value: (x?: string, y?: string) => y ?? x,
      default: () => undefined,
    },
    response: {
      value: (x?: string, y?: string) => y ?? x,
      default: () => undefined,
    },
  },
});

// 处理用户输入的节点
async function processInput(state: ChatState): Promise<Partial<ChatState>> {
  const { user_input, messages } = state;
  
  if (user_input) {
    // 简单的回复逻辑
    const response = `Echo: ${user_input}`;
    
    return {
      messages: [`User: ${user_input}`, `Bot: ${response}`],
      response: response,
    };
  }
  
  return {};
}

// 添加节点
graph.addNode("process_input", processInput);

// 设置入口点
graph.setEntryPoint("process_input");

// 设置结束点
graph.setFinishPoint("process_input");

// 编译图
export const compiledGraph = graph.compile();
export default compiledGraph;
