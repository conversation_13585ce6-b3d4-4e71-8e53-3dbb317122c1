diff --git a/Objects/fileobject.c b/Objects/fileobject.c
index e79a671..b1bd74e 100644
--- a/Objects/fileobject.c
+++ b/Objects/fileobject.c
@@ -336,7 +336,7 @@ file_read(f, args)
 */
 
 static object *
-getline(f, n)
+_py_getline(f, n)
 	fileobject *f;
 	int n;
 {
@@ -458,7 +458,7 @@ filegetline(f, n)
 	}
 	if (((fileobject*)f)->f_fp == NULL)
 		return err_closed();
-	return getline((fileobject *)f, n);
+	return _py_getline((fileobject *)f, n);
 }
 
 /* Python method */
@@ -483,7 +483,7 @@ file_readline(f, args)
 		}
 	}
 
-	return getline(f, n);
+	return _py_getline(f, n);
 }
 
 static object *
@@ -501,7 +501,7 @@ file_readlines(f, args)
 	if ((list = newlistobject(0)) == NULL)
 		return NULL;
 	for (;;) {
-		line = getline(f, 0);
+		line = _py_getline(f, 0);
 		if (line != NULL && getstringsize(line) == 0) {
 			DECREF(line);
 			break;
