diff -rupN a/Include/objimpl.h b/Include/objimpl.h
--- a/Include/objimpl.h	2006-04-14 20:22:46.000000000 -0700
+++ b/Include/objimpl.h	2019-09-30 15:52:15.197269278 -0700
@@ -241,6 +241,20 @@ PyAPI_FUNC(PyVarObject *) _PyObject_GC_R
 /* for source compatibility with 2.2 */
 #define _PyObject_GC_Del PyObject_GC_Del
 
+/*
+ * Former over-aligned definition of PyGC_Head, used to compute the size of the
+ * padding for the new version below.
+ */
+union _gc_head;
+union _gc_head_old {
+	struct {
+		union _gc_head_old *gc_next;
+		union _gc_head_old *gc_prev;
+		Py_ssize_t gc_refs;
+	} gc;
+	long double dummy;
+};
+
 /* GC information is stored BEFORE the object structure. */
 typedef union _gc_head {
 	struct {
@@ -248,7 +262,8 @@ typedef union _gc_head {
 		union _gc_head *gc_prev;
 		Py_ssize_t gc_refs;
 	} gc;
-	long double dummy;  /* force worst-case alignment */
+	double dummy; /* Force at least 8-byte alignment. */
+	char dummy_padding[sizeof(union _gc_head_old)];
 } PyGC_Head;
 
 extern PyGC_Head *_PyGC_generation0;
diff -rupN a/Makefile.pre.in b/Makefile.pre.in
--- a/Makefile.pre.in	2008-09-21 17:22:44.000000000 -0700
+++ b/Makefile.pre.in	2019-09-30 15:50:47.804578897 -0700
@@ -458,7 +458,7 @@ Modules/getbuildinfo.o: $(PARSER_OBJS) \
 		$(SIGNAL_OBJS) \
 		$(MODOBJS) \
 		$(srcdir)/Modules/getbuildinfo.c
-	$(CC) -c $(PY_CFLAGS) -DSVNVERSION=\"`LC_ALL=C $(SVNVERSION)`\" -o $@ $(srcdir)/Modules/getbuildinfo.c
+	$(CC) -c $(PY_CFLAGS) -DSVNVERSION="\"`LC_ALL=C $(SVNVERSION)`\"" -o $@ $(srcdir)/Modules/getbuildinfo.c
 
 Modules/getpath.o: $(srcdir)/Modules/getpath.c Makefile
 	$(CC) -c $(PY_CFLAGS) -DPYTHONPATH='"$(PYTHONPATH)"' \
diff -rupN a/setup.py b/setup.py
--- a/setup.py	2008-10-16 11:58:19.000000000 -0700
+++ b/setup.py	2019-09-30 15:50:47.808578930 -0700
@@ -296,7 +296,8 @@ class PyBuildExt(build_ext):
         # if a file is found in one of those directories, it can
         # be assumed that no additional -I,-L directives are needed.
         lib_dirs = self.compiler.library_dirs + [
-            '/lib64', '/usr/lib64',
+            '/usr/lib/x86_64-linux-gnu',
+            '/lib/x86_64-linux-gnu',
             '/lib', '/usr/lib',
             ]
         inc_dirs = self.compiler.include_dirs + ['/usr/include']
@@ -711,9 +712,9 @@ class PyBuildExt(build_ext):
 
                 # check lib directories parallel to the location of the header
                 db_dirs_to_check = [
-                    os.path.join(db_incdir, '..', 'lib64'),
+                    os.path.join(db_incdir, '..', 'lib/x86_64-linux-gnu'),
                     os.path.join(db_incdir, '..', 'lib'),
-                    os.path.join(db_incdir, '..', '..', 'lib64'),
+                    os.path.join(db_incdir, '..', '..', 'lib/x86_64-linux-gnu'),
                     os.path.join(db_incdir, '..', '..', 'lib'),
                 ]
                 db_dirs_to_check = filter(os.path.isdir, db_dirs_to_check)
@@ -800,9 +801,9 @@ class PyBuildExt(build_ext):
 
         if sqlite_incdir:
             sqlite_dirs_to_check = [
-                os.path.join(sqlite_incdir, '..', 'lib64'),
+                os.path.join(sqlite_incdir, '..', 'lib/x86_64-linux-gnu'),
                 os.path.join(sqlite_incdir, '..', 'lib'),
-                os.path.join(sqlite_incdir, '..', '..', 'lib64'),
+                os.path.join(sqlite_incdir, '..', '..', 'lib/x86_64-linux-gnu'),
                 os.path.join(sqlite_incdir, '..', '..', 'lib'),
             ]
             sqlite_libfile = self.compiler.find_library_file(
