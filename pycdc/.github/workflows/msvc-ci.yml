name: MSVC-CI
on:
  push:
    branches: [master]
  pull_request:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - name: Configure and Build
        run: |
          mkdir build
          cd build
          cmake -G "Visual Studio 17 2022" -A Win32 ..
          cmake --build . --config Debug
          cmake --build . --config Release

      - name: Test
        run: |
          cmake --build build --config Debug --target check
          cmake --build build --config Release --target check

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: pycdc-release
          path: build\Release\*.exe
