#include "bytecode_map.h"

BEGIN_MAP(3, 12)
    MAP_OP(0, CACHE)
    MAP_OP(1, POP_TOP)
    MAP_OP(2, PUSH_NULL)
    MAP_OP(3, INTERPRETER_EXIT)
    MAP_OP(4, END_FOR)
    MAP_OP(5, <PERSON><PERSON>_SEND)
    MAP_OP(9, NOP)
    MAP_OP(11, UNARY_NEGATIVE)
    MAP_OP(12, UNARY_NOT)
    MAP_OP(15, UNARY_INVERT)
    MAP_OP(17, RESERVED)
    MAP_OP(25, BINARY_SUBSCR)
    MAP_OP(26, BINARY_SLICE)
    MAP_OP(27, STORE_SLICE)
    MAP_OP(30, GET_<PERSON>EN)
    MAP_OP(31, MATCH_MAPPING)
    MAP_OP(32, MATCH_SEQUENCE)
    MAP_OP(33, MATCH_KEYS)
    MAP_OP(35, PUSH_EXC_INFO)
    MAP_OP(36, CHECK_EXC_MATCH)
    MAP_OP(37, CHECK_EG_MATCH)
    MAP_OP(49, WITH_EXCEPT_START)
    MAP_OP(50, GET_AITER)
    MAP_OP(51, GET_ANEXT)
    MAP_OP(52, BEFORE_ASYNC_WITH)
    MAP_OP(53, BEFORE_WITH)
    MAP_OP(54, END_ASYNC_FOR)
    MAP_OP(55, CLEANUP_THROW)
    MAP_OP(60, STORE_SUBSCR)
    MAP_OP(61, DELETE_SUBSCR)
    MAP_OP(68, GET_ITER)
    MAP_OP(69, GET_YIELD_FROM_ITER)
    MAP_OP(71, LOAD_BUILD_CLASS)
    MAP_OP(74, LOAD_ASSERTION_ERROR)
    MAP_OP(75, RETURN_GENERATOR)
    MAP_OP(83, RETURN_VALUE)
    MAP_OP(85, SETUP_ANNOTATIONS)
    MAP_OP(87, LOAD_LOCALS)
    MAP_OP(89, POP_EXCEPT)
    MAP_OP(90, STORE_NAME_A)
    MAP_OP(91, DELETE_NAME_A)
    MAP_OP(92, UNPACK_SEQUENCE_A)
    MAP_OP(93, FOR_ITER_A)
    MAP_OP(94, UNPACK_EX_A)
    MAP_OP(95, STORE_ATTR_A)
    MAP_OP(96, DELETE_ATTR_A)
    MAP_OP(97, STORE_GLOBAL_A)
    MAP_OP(98, DELETE_GLOBAL_A)
    MAP_OP(99, SWAP_A)
    MAP_OP(100, LOAD_CONST_A)
    MAP_OP(101, LOAD_NAME_A)
    MAP_OP(102, BUILD_TUPLE_A)
    MAP_OP(103, BUILD_LIST_A)
    MAP_OP(104, BUILD_SET_A)
    MAP_OP(105, BUILD_MAP_A)
    MAP_OP(106, LOAD_ATTR_A)
    MAP_OP(107, COMPARE_OP_A)
    MAP_OP(108, IMPORT_NAME_A)
    MAP_OP(109, IMPORT_FROM_A)
    MAP_OP(110, JUMP_FORWARD_A)
    MAP_OP(114, POP_JUMP_IF_FALSE_A)
    MAP_OP(115, POP_JUMP_IF_TRUE_A)
    MAP_OP(116, LOAD_GLOBAL_A)
    MAP_OP(117, IS_OP_A)
    MAP_OP(118, CONTAINS_OP_A)
    MAP_OP(119, RERAISE_A)
    MAP_OP(120, COPY_A)
    MAP_OP(121, RETURN_CONST_A)
    MAP_OP(122, BINARY_OP_A)
    MAP_OP(123, SEND_A)
    MAP_OP(124, LOAD_FAST_A)
    MAP_OP(125, STORE_FAST_A)
    MAP_OP(126, DELETE_FAST_A)
    MAP_OP(127, LOAD_FAST_CHECK_A)
    MAP_OP(128, POP_JUMP_IF_NOT_NONE_A)
    MAP_OP(129, POP_JUMP_IF_NONE_A)
    MAP_OP(130, RAISE_VARARGS_A)
    MAP_OP(131, GET_AWAITABLE_A)
    MAP_OP(132, MAKE_FUNCTION_A)
    MAP_OP(133, BUILD_SLICE_A)
    MAP_OP(134, JUMP_BACKWARD_NO_INTERRUPT_A)
    MAP_OP(135, MAKE_CELL_A)
    MAP_OP(136, LOAD_CLOSURE_A)
    MAP_OP(137, LOAD_DEREF_A)
    MAP_OP(138, STORE_DEREF_A)
    MAP_OP(139, DELETE_DEREF_A)
    MAP_OP(140, JUMP_BACKWARD_A)
    MAP_OP(141, LOAD_SUPER_ATTR_A)
    MAP_OP(142, CALL_FUNCTION_EX_A)
    MAP_OP(143, LOAD_FAST_AND_CLEAR_A)
    MAP_OP(144, EXTENDED_ARG_A)
    MAP_OP(145, LIST_APPEND_A)
    MAP_OP(146, SET_ADD_A)
    MAP_OP(147, MAP_ADD_A)
    MAP_OP(149, COPY_FREE_VARS_A)
    MAP_OP(150, YIELD_VALUE_A)
    MAP_OP(151, RESUME_A)
    MAP_OP(152, MATCH_CLASS_A)
    MAP_OP(155, FORMAT_VALUE_A)
    MAP_OP(156, BUILD_CONST_KEY_MAP_A)
    MAP_OP(157, BUILD_STRING_A)
    MAP_OP(162, LIST_EXTEND_A)
    MAP_OP(163, SET_UPDATE_A)
    MAP_OP(164, DICT_MERGE_A)
    MAP_OP(165, DICT_UPDATE_A)
    MAP_OP(171, CALL_A)
    MAP_OP(172, KW_NAMES_A)
    MAP_OP(173, CALL_INTRINSIC_1_A)
    MAP_OP(174, CALL_INTRINSIC_2_A)
    MAP_OP(175, LOAD_FROM_DICT_OR_GLOBALS_A)
    MAP_OP(176, LOAD_FROM_DICT_OR_DEREF_A)
    MAP_OP(237, INSTRUMENTED_LOAD_SUPER_ATTR_A)
    MAP_OP(238, INSTRUMENTED_POP_JUMP_IF_NONE_A)
    MAP_OP(239, INSTRUMENTED_POP_JUMP_IF_NOT_NONE_A)
    MAP_OP(240, INSTRUMENTED_RESUME_A)
    MAP_OP(241, INSTRUMENTED_CALL_A)
    MAP_OP(242, INSTRUMENTED_RETURN_VALUE_A)
    MAP_OP(243, INSTRUMENTED_YIELD_VALUE_A)
    MAP_OP(244, INSTRUMENTED_CALL_FUNCTION_EX_A)
    MAP_OP(245, INSTRUMENTED_JUMP_FORWARD_A)
    MAP_OP(246, INSTRUMENTED_JUMP_BACKWARD_A)
    MAP_OP(247, INSTRUMENTED_RETURN_CONST_A)
    MAP_OP(248, INSTRUMENTED_FOR_ITER_A)
    MAP_OP(249, INSTRUMENTED_POP_JUMP_IF_FALSE_A)
    MAP_OP(250, INSTRUMENTED_POP_JUMP_IF_TRUE_A)
    MAP_OP(251, INSTRUMENTED_END_FOR_A)
    MAP_OP(252, INSTRUMENTED_END_SEND_A)
    MAP_OP(253, INSTRUMENTED_INSTRUCTION_A)
    MAP_OP(254, INSTRUMENTED_LINE_A)
END_MAP()
