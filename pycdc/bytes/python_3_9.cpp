#include "bytecode_map.h"

BEGIN_MAP(3, 9)
    MAP_OP(1, POP_TOP)
    MAP_OP(2, ROT_TWO)
    MAP_OP(3, ROT_THREE)
    MAP_OP(4, DUP_TOP)
    MAP_OP(5, DUP_TOP_TWO)
    MAP_OP(6, ROT_FOUR)
    MAP_OP(9, NOP)
    MAP_OP(10, UNARY_POSITIVE)
    MAP_OP(11, UNARY_NEGATIVE)
    MAP_OP(12, UNARY_NOT)
    MAP_OP(15, UNARY_INVERT)
    MAP_OP(16, BINARY_MATRIX_MULTIPLY)
    MAP_OP(17, INPLACE_MATRIX_MULTIPLY)
    MAP_OP(19, BINARY_POWER)
    MAP_OP(20, BINARY_MULTIPLY)
    MAP_OP(22, BINARY_MODULO)
    MAP_OP(23, <PERSON>INARY_ADD)
    MAP_OP(24, <PERSON><PERSON>ARY_SUBTRACT)
    MAP_OP(25, <PERSON><PERSON><PERSON>Y_SUBSCR)
    MAP_OP(26, <PERSON><PERSON><PERSON>Y_FLOOR_DIVIDE)
    MAP_OP(27, <PERSON><PERSON><PERSON><PERSON>_TRUE_DIVIDE)
    MAP_OP(28, INPLACE_FLOOR_DIVIDE)
    MAP_OP(29, INPLACE_TRUE_DIVIDE)
    MAP_OP(48, RERAISE)
    MAP_OP(49, WITH_EXCEPT_START)
    MAP_OP(50, GET_AITER)
    MAP_OP(51, GET_ANEXT)
    MAP_OP(52, BEFORE_ASYNC_WITH)
    MAP_OP(54, END_ASYNC_FOR)
    MAP_OP(55, INPLACE_ADD)
    MAP_OP(56, INPLACE_SUBTRACT)
    MAP_OP(57, INPLACE_MULTIPLY)
    MAP_OP(59, INPLACE_MODULO)
    MAP_OP(60, STORE_SUBSCR)
    MAP_OP(61, DELETE_SUBSCR)
    MAP_OP(62, BINARY_LSHIFT)
    MAP_OP(63, BINARY_RSHIFT)
    MAP_OP(64, BINARY_AND)
    MAP_OP(65, BINARY_XOR)
    MAP_OP(66, BINARY_OR)
    MAP_OP(67, INPLACE_POWER)
    MAP_OP(68, GET_ITER)
    MAP_OP(69, GET_YIELD_FROM_ITER)
    MAP_OP(70, PRINT_EXPR)
    MAP_OP(71, LOAD_BUILD_CLASS)
    MAP_OP(72, YIELD_FROM)
    MAP_OP(73, GET_AWAITABLE)
    MAP_OP(74, LOAD_ASSERTION_ERROR)
    MAP_OP(75, INPLACE_LSHIFT)
    MAP_OP(76, INPLACE_RSHIFT)
    MAP_OP(77, INPLACE_AND)
    MAP_OP(78, INPLACE_XOR)
    MAP_OP(79, INPLACE_OR)
    MAP_OP(82, LIST_TO_TUPLE)
    MAP_OP(83, RETURN_VALUE)
    MAP_OP(84, IMPORT_STAR)
    MAP_OP(85, SETUP_ANNOTATIONS)
    MAP_OP(86, YIELD_VALUE)
    MAP_OP(87, POP_BLOCK)
    MAP_OP(89, POP_EXCEPT)
    MAP_OP(90, STORE_NAME_A)
    MAP_OP(91, DELETE_NAME_A)
    MAP_OP(92, UNPACK_SEQUENCE_A)
    MAP_OP(93, FOR_ITER_A)
    MAP_OP(94, UNPACK_EX_A)
    MAP_OP(95, STORE_ATTR_A)
    MAP_OP(96, DELETE_ATTR_A)
    MAP_OP(97, STORE_GLOBAL_A)
    MAP_OP(98, DELETE_GLOBAL_A)
    MAP_OP(100, LOAD_CONST_A)
    MAP_OP(101, LOAD_NAME_A)
    MAP_OP(102, BUILD_TUPLE_A)
    MAP_OP(103, BUILD_LIST_A)
    MAP_OP(104, BUILD_SET_A)
    MAP_OP(105, BUILD_MAP_A)
    MAP_OP(106, LOAD_ATTR_A)
    MAP_OP(107, COMPARE_OP_A)
    MAP_OP(108, IMPORT_NAME_A)
    MAP_OP(109, IMPORT_FROM_A)
    MAP_OP(110, JUMP_FORWARD_A)
    MAP_OP(111, JUMP_IF_FALSE_OR_POP_A)
    MAP_OP(112, JUMP_IF_TRUE_OR_POP_A)
    MAP_OP(113, JUMP_ABSOLUTE_A)
    MAP_OP(114, POP_JUMP_IF_FALSE_A)
    MAP_OP(115, POP_JUMP_IF_TRUE_A)
    MAP_OP(116, LOAD_GLOBAL_A)
    MAP_OP(117, IS_OP_A)
    MAP_OP(118, CONTAINS_OP_A)
    MAP_OP(121, JUMP_IF_NOT_EXC_MATCH_A)
    MAP_OP(122, SETUP_FINALLY_A)
    MAP_OP(124, LOAD_FAST_A)
    MAP_OP(125, STORE_FAST_A)
    MAP_OP(126, DELETE_FAST_A)
    MAP_OP(130, RAISE_VARARGS_A)
    MAP_OP(131, CALL_FUNCTION_A)
    MAP_OP(132, MAKE_FUNCTION_A)
    MAP_OP(133, BUILD_SLICE_A)
    MAP_OP(135, LOAD_CLOSURE_A)
    MAP_OP(136, LOAD_DEREF_A)
    MAP_OP(137, STORE_DEREF_A)
    MAP_OP(138, DELETE_DEREF_A)
    MAP_OP(141, CALL_FUNCTION_KW_A)
    MAP_OP(142, CALL_FUNCTION_EX_A)
    MAP_OP(143, SETUP_WITH_A)
    MAP_OP(144, EXTENDED_ARG_A)
    MAP_OP(145, LIST_APPEND_A)
    MAP_OP(146, SET_ADD_A)
    MAP_OP(147, MAP_ADD_A)
    MAP_OP(148, LOAD_CLASSDEREF_A)
    MAP_OP(154, SETUP_ASYNC_WITH_A)
    MAP_OP(155, FORMAT_VALUE_A)
    MAP_OP(156, BUILD_CONST_KEY_MAP_A)
    MAP_OP(157, BUILD_STRING_A)
    MAP_OP(160, LOAD_METHOD_A)
    MAP_OP(161, CALL_METHOD_A)
    MAP_OP(162, LIST_EXTEND_A)
    MAP_OP(163, SET_UPDATE_A)
    MAP_OP(164, DICT_MERGE_A)
    MAP_OP(165, DICT_UPDATE_A)
END_MAP()
