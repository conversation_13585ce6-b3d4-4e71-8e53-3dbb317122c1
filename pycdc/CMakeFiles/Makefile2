# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/pycxx.dir/all
all: CMakeFiles/pycdas.dir/all
all: CMakeFiles/pycdc.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/pycxx.dir/codegen
codegen: CMakeFiles/pycdas.dir/codegen
codegen: CMakeFiles/pycdc.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/pycxx.dir/clean
clean: CMakeFiles/pycdas.dir/clean
clean: CMakeFiles/pycdc.dir/clean
clean: CMakeFiles/check.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/pycxx.dir

# All Build rule for target.
CMakeFiles/pycxx.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycxx.dir/build.make CMakeFiles/pycxx.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycxx.dir/build.make CMakeFiles/pycxx.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43 "Built target pycxx"
.PHONY : CMakeFiles/pycxx.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pycxx.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pycxx.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 0
.PHONY : CMakeFiles/pycxx.dir/rule

# Convenience name for target.
pycxx: CMakeFiles/pycxx.dir/rule
.PHONY : pycxx

# codegen rule for target.
CMakeFiles/pycxx.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycxx.dir/build.make CMakeFiles/pycxx.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43 "Finished codegen for target pycxx"
.PHONY : CMakeFiles/pycxx.dir/codegen

# clean rule for target.
CMakeFiles/pycxx.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycxx.dir/build.make CMakeFiles/pycxx.dir/clean
.PHONY : CMakeFiles/pycxx.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/pycdas.dir

# All Build rule for target.
CMakeFiles/pycdas.dir/all: CMakeFiles/pycxx.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdas.dir/build.make CMakeFiles/pycdas.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdas.dir/build.make CMakeFiles/pycdas.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=1,2 "Built target pycdas"
.PHONY : CMakeFiles/pycdas.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pycdas.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 39
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pycdas.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 0
.PHONY : CMakeFiles/pycdas.dir/rule

# Convenience name for target.
pycdas: CMakeFiles/pycdas.dir/rule
.PHONY : pycdas

# codegen rule for target.
CMakeFiles/pycdas.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdas.dir/build.make CMakeFiles/pycdas.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=1,2 "Finished codegen for target pycdas"
.PHONY : CMakeFiles/pycdas.dir/codegen

# clean rule for target.
CMakeFiles/pycdas.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdas.dir/build.make CMakeFiles/pycdas.dir/clean
.PHONY : CMakeFiles/pycdas.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/pycdc.dir

# All Build rule for target.
CMakeFiles/pycdc.dir/all: CMakeFiles/pycxx.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdc.dir/build.make CMakeFiles/pycdc.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdc.dir/build.make CMakeFiles/pycdc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=3,4,5,6 "Built target pycdc"
.PHONY : CMakeFiles/pycdc.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pycdc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 41
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pycdc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 0
.PHONY : CMakeFiles/pycdc.dir/rule

# Convenience name for target.
pycdc: CMakeFiles/pycdc.dir/rule
.PHONY : pycdc

# codegen rule for target.
CMakeFiles/pycdc.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdc.dir/build.make CMakeFiles/pycdc.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=3,4,5,6 "Finished codegen for target pycdc"
.PHONY : CMakeFiles/pycdc.dir/codegen

# clean rule for target.
CMakeFiles/pycdc.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pycdc.dir/build.make CMakeFiles/pycdc.dir/clean
.PHONY : CMakeFiles/pycdc.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/check.dir

# All Build rule for target.
CMakeFiles/check.dir/all: CMakeFiles/pycdc.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num= "Built target check"
.PHONY : CMakeFiles/check.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 41
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles 0
.PHONY : CMakeFiles/check.dir/rule

# Convenience name for target.
check: CMakeFiles/check.dir/rule
.PHONY : check

# codegen rule for target.
CMakeFiles/check.dir/codegen: CMakeFiles/pycdc.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num= "Finished codegen for target check"
.PHONY : CMakeFiles/check.dir/codegen

# clean rule for target.
CMakeFiles/check.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/check.dir/build.make CMakeFiles/check.dir/clean
.PHONY : CMakeFiles/check.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

