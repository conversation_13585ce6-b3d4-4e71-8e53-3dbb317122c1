
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/uname"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/uname"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/uname"
      - "/usr/local/bin/uname"
      - "/System/Cryptexes/App/usr/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Darwin - 24.3.0 - arm64
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/gmake"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/gmake"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/gmake"
      - "/usr/local/bin/gmake"
      - "/System/Cryptexes/App/usr/bin/gmake"
      - "/usr/bin/gmake"
      - "/bin/gmake"
      - "/usr/sbin/gmake"
      - "/sbin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/gmake"
      - "/Library/Apple/usr/bin/gmake"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/gmake"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/gmake"
      - "/Users/<USER>/.codeium/windsurf/bin/gmake"
      - "/Users/<USER>/Library/pnpm/gmake"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/gmake"
      - "/Users/<USER>/.asdf/shims/gmake"
      - "/opt/homebrew/opt/asdf/libexec/bin/gmake"
      - "/opt/homebrew/bin/gmake"
      - "/opt/homebrew/sbin/gmake"
      - "/Users/<USER>/.local/bin/gmake"
      - "/Users/<USER>/.cargo/bin/gmake"
      - "/Users/<USER>/.orbstack/bin/gmake"
      - "/Users/<USER>/.docker/bin/gmake"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/gmake"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/make"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/make"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/make"
      - "/usr/local/bin/make"
      - "/System/Cryptexes/App/usr/bin/make"
    found: "/usr/bin/make"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/cc"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/cc"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/cc"
      - "/usr/local/bin/cc"
      - "/System/Cryptexes/App/usr/bin/cc"
    found: "/usr/bin/cc"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/4.1.0/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/cc" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 424 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 177 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 178 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 179 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/usr/bin/objcopy"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/objcopy"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/objcopy"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/objcopy"
      - "/usr/local/bin/objcopy"
      - "/System/Cryptexes/App/usr/bin/objcopy"
      - "/bin/objcopy"
      - "/usr/sbin/objcopy"
      - "/sbin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/objcopy"
      - "/Library/Apple/usr/bin/objcopy"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/objcopy"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/objcopy"
      - "/Users/<USER>/.codeium/windsurf/bin/objcopy"
      - "/Users/<USER>/Library/pnpm/objcopy"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/objcopy"
      - "/Users/<USER>/.asdf/shims/objcopy"
      - "/opt/homebrew/opt/asdf/libexec/bin/objcopy"
      - "/opt/homebrew/bin/objcopy"
      - "/opt/homebrew/sbin/objcopy"
      - "/Users/<USER>/.local/bin/objcopy"
      - "/Users/<USER>/.cargo/bin/objcopy"
      - "/Users/<USER>/.orbstack/bin/objcopy"
      - "/Users/<USER>/.docker/bin/objcopy"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/objcopy"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/usr/bin/readelf"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/readelf"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/readelf"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/readelf"
      - "/usr/local/bin/readelf"
      - "/System/Cryptexes/App/usr/bin/readelf"
      - "/bin/readelf"
      - "/usr/sbin/readelf"
      - "/sbin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/readelf"
      - "/Library/Apple/usr/bin/readelf"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/readelf"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/readelf"
      - "/Users/<USER>/.codeium/windsurf/bin/readelf"
      - "/Users/<USER>/Library/pnpm/readelf"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/readelf"
      - "/Users/<USER>/.asdf/shims/readelf"
      - "/opt/homebrew/opt/asdf/libexec/bin/readelf"
      - "/opt/homebrew/bin/readelf"
      - "/opt/homebrew/sbin/readelf"
      - "/Users/<USER>/.local/bin/readelf"
      - "/Users/<USER>/.cargo/bin/readelf"
      - "/Users/<USER>/.orbstack/bin/readelf"
      - "/Users/<USER>/.docker/bin/readelf"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/readelf"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/usr/bin/dlltool"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/dlltool"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/dlltool"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/System/Cryptexes/App/usr/bin/dlltool"
      - "/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/dlltool"
      - "/Library/Apple/usr/bin/dlltool"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/dlltool"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/dlltool"
      - "/Users/<USER>/.codeium/windsurf/bin/dlltool"
      - "/Users/<USER>/Library/pnpm/dlltool"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/dlltool"
      - "/Users/<USER>/.asdf/shims/dlltool"
      - "/opt/homebrew/opt/asdf/libexec/bin/dlltool"
      - "/opt/homebrew/bin/dlltool"
      - "/opt/homebrew/sbin/dlltool"
      - "/Users/<USER>/.local/bin/dlltool"
      - "/Users/<USER>/.cargo/bin/dlltool"
      - "/Users/<USER>/.orbstack/bin/dlltool"
      - "/Users/<USER>/.docker/bin/dlltool"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/usr/bin/addr2line"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/addr2line"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/addr2line"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/addr2line"
      - "/usr/local/bin/addr2line"
      - "/System/Cryptexes/App/usr/bin/addr2line"
      - "/bin/addr2line"
      - "/usr/sbin/addr2line"
      - "/sbin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/addr2line"
      - "/Library/Apple/usr/bin/addr2line"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/addr2line"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/addr2line"
      - "/Users/<USER>/.codeium/windsurf/bin/addr2line"
      - "/Users/<USER>/Library/pnpm/addr2line"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/addr2line"
      - "/Users/<USER>/.asdf/shims/addr2line"
      - "/opt/homebrew/opt/asdf/libexec/bin/addr2line"
      - "/opt/homebrew/bin/addr2line"
      - "/opt/homebrew/sbin/addr2line"
      - "/Users/<USER>/.local/bin/addr2line"
      - "/Users/<USER>/.cargo/bin/addr2line"
      - "/Users/<USER>/.orbstack/bin/addr2line"
      - "/Users/<USER>/.docker/bin/addr2line"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/addr2line"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/tapi"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/tapi"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/tapi"
      - "/usr/local/bin/tapi"
      - "/System/Cryptexes/App/usr/bin/tapi"
      - "/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/tapi"
      - "/Library/Apple/usr/bin/tapi"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/tapi"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/tapi"
      - "/Users/<USER>/.codeium/windsurf/bin/tapi"
      - "/Users/<USER>/Library/pnpm/tapi"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/tapi"
      - "/Users/<USER>/.asdf/shims/tapi"
      - "/opt/homebrew/opt/asdf/libexec/bin/tapi"
      - "/opt/homebrew/bin/tapi"
      - "/opt/homebrew/sbin/tapi"
      - "/Users/<USER>/.local/bin/tapi"
      - "/Users/<USER>/.cargo/bin/tapi"
      - "/Users/<USER>/.orbstack/bin/tapi"
      - "/Users/<USER>/.docker/bin/tapi"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/usr/bin/"
    found: "/usr/bin/c++"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/4.1.0/CompilerIdCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler apple sysroot: "/usr/bin/c++" "-E" "apple-sdk.cpp"
        # 1 "apple-sdk.cpp"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 439 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.cpp" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 177 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 178 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 179 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.cpp" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake:76 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake:32 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_INSTALL_NAME_TOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "install_name_tool"
    candidate_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/condabin/"
      - "/Users/<USER>/.codeium/windsurf/bin/"
      - "/Users/<USER>/Library/pnpm/"
      - "/Library/Frameworks/Python.framework/Versions/2.7/bin/"
      - "/Users/<USER>/.asdf/shims/"
      - "/opt/homebrew/opt/asdf/libexec/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Users/<USER>/.local/bin/"
      - "/Users/<USER>/.cargo/bin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.docker/bin/"
      - "/Users/<USER>/projs/langgraph-api-js/pycdc/"
    searched_directories:
      - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/install_name_tool"
      - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin/install_name_tool"
      - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin/install_name_tool"
      - "/usr/local/bin/install_name_tool"
      - "/System/Cryptexes/App/usr/bin/install_name_tool"
    found: "/usr/bin/install_name_tool"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
      CMAKE_INSTALL_PREFIX: "/usr/local"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba"
      binary: "/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_acf57/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_acf57.dir/build.make CMakeFiles/cmTC_acf57.dir/build
        Building C object CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_acf57
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_acf57.dir/link.txt --verbose=1
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_acf57 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 23:52:02 Dec  5 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/cc  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -o cmTC_acf57
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_acf57/fast]
        ignore line: [/Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_acf57.dir/build.make CMakeFiles/cmTC_acf57.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-VJPIba -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_acf57]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_acf57.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_acf57 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.2] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_acf57] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_acf57.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 23:52:02 Dec  5 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9"
      binary: "/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_73935/fast
        /Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_73935.dir/build.make CMakeFiles/cmTC_73935.dir/build
        Building CXX object CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_73935
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_73935.dir/link.txt --verbose=1
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_73935 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 23:52:02 Dec  5 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_73935
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_73935/fast]
        ignore line: [/Applications/Xcode.app/Contents/Developer/usr/bin/make  -f CMakeFiles/cmTC_73935.dir/build.make CMakeFiles/cmTC_73935.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9 -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/CMakeScratch/TryCompile-HrsKD9 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_73935]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_73935.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_73935 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.2] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_73935] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_73935.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 23:52:02 Dec  5 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindPython/Support.cmake:2137 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FindPython3.cmake:656 (include)"
      - "CMakeLists.txt:79 (find_package)"
    mode: "program"
    variable: "_Python3_EXECUTABLE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "python3.14mu"
      - "python3.14m"
      - "python3.14u"
      - "python3.14"
      - "python3.14dmu"
      - "python3.14dm"
      - "python3.14du"
      - "python3.14d"
      - "python3"
      - "python"
    candidate_directories:
      - "/opt/homebrew/Caskroom/miniconda/base/bin/"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/"
      - "/opt/homebrew/Caskroom/miniconda/base/"
    searched_directories:
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14mu"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14m"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14u"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14dmu"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14dm"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14du"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3.14d"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python3"
      - "/opt/homebrew/Caskroom/miniconda/base/bin/python"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14mu"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14m"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14u"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14dmu"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14dm"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14du"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3.14d"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python3"
      - "/opt/homebrew/Caskroom/miniconda/base/Scripts/python"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14mu"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14m"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14u"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14dmu"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14dm"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14du"
      - "/opt/homebrew/Caskroom/miniconda/base/python3.14d"
      - "/opt/homebrew/Caskroom/miniconda/base/python3"
      - "/opt/homebrew/Caskroom/miniconda/base/python"
    found: false
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/FindPython/Support.cmake:2158 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/FindPython3.cmake:656 (include)"
      - "CMakeLists.txt:79 (find_package)"
    mode: "program"
    variable: "_Python3_EXECUTABLE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "python3.13mu"
      - "python3.13m"
      - "python3.13u"
      - "python3.13"
      - "python3.13dmu"
      - "python3.13dm"
      - "python3.13du"
      - "python3.13d"
      - "python3"
      - "python"
    candidate_directories:
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/Scripts/"
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/"
    searched_directories:
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/bin/python3.13mu"
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/bin/python3.13m"
      - "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/bin/python3.13u"
    found: "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/bin/python3.13"
    search_context:
      ENV{PATH}:
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Users/<USER>/projs/configs/apache-maven-3.8.6/bin"
        - "/Library/Java/JavaVirtualMachines/jdk-19.jdk/Contents/Home/bin"
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/bin"
        - "/opt/homebrew/Caskroom/miniconda/base/condabin"
        - "/Users/<USER>/.codeium/windsurf/bin"
        - "/Users/<USER>/Library/pnpm"
        - "/Users/<USER>/.nvm/versions/node/v20.19.0/bin"
        - "/Library/Frameworks/Python.framework/Versions/2.7/bin"
        - "/Users/<USER>/.asdf/shims"
        - "/opt/homebrew/opt/asdf/libexec/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Users/<USER>/.local/bin"
        - "/Users/<USER>/.cargo/bin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.docker/bin"
        - "."
      CMAKE_INSTALL_PREFIX: "/usr/local"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr"
        - "/"
        - "/opt/homebrew"
        - "/usr/local"
        - "/usr/X11R6"
        - "/usr/pkg"
        - "/opt"
        - "/sw"
        - "/opt/local"
      CMAKE_SYSTEM_APPBUNDLE_PATH:
        - "/Users/<USER>/Applications"
        - "/Applications"
        - "/Applications/Xcode.app/Contents/Applications"
        - "/Applications/Xcode.app/Contents/Developer/Applications"
...
