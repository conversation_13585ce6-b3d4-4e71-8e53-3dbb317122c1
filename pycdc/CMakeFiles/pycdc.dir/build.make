# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

# Include any dependencies generated for this target.
include CMakeFiles/pycdc.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/pycdc.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pycdc.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/pycdc.dir/flags.make

CMakeFiles/pycdc.dir/codegen:
.PHONY : CMakeFiles/pycdc.dir/codegen

CMakeFiles/pycdc.dir/pycdc.cpp.o: CMakeFiles/pycdc.dir/flags.make
CMakeFiles/pycdc.dir/pycdc.cpp.o: pycdc.cpp
CMakeFiles/pycdc.dir/pycdc.cpp.o: CMakeFiles/pycdc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/pycdc.dir/pycdc.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pycdc.dir/pycdc.cpp.o -MF CMakeFiles/pycdc.dir/pycdc.cpp.o.d -o CMakeFiles/pycdc.dir/pycdc.cpp.o -c /Users/<USER>/projs/langgraph-api-js/pycdc/pycdc.cpp

CMakeFiles/pycdc.dir/pycdc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/pycdc.dir/pycdc.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/projs/langgraph-api-js/pycdc/pycdc.cpp > CMakeFiles/pycdc.dir/pycdc.cpp.i

CMakeFiles/pycdc.dir/pycdc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/pycdc.dir/pycdc.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/projs/langgraph-api-js/pycdc/pycdc.cpp -o CMakeFiles/pycdc.dir/pycdc.cpp.s

CMakeFiles/pycdc.dir/ASTree.cpp.o: CMakeFiles/pycdc.dir/flags.make
CMakeFiles/pycdc.dir/ASTree.cpp.o: ASTree.cpp
CMakeFiles/pycdc.dir/ASTree.cpp.o: CMakeFiles/pycdc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/pycdc.dir/ASTree.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pycdc.dir/ASTree.cpp.o -MF CMakeFiles/pycdc.dir/ASTree.cpp.o.d -o CMakeFiles/pycdc.dir/ASTree.cpp.o -c /Users/<USER>/projs/langgraph-api-js/pycdc/ASTree.cpp

CMakeFiles/pycdc.dir/ASTree.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/pycdc.dir/ASTree.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/projs/langgraph-api-js/pycdc/ASTree.cpp > CMakeFiles/pycdc.dir/ASTree.cpp.i

CMakeFiles/pycdc.dir/ASTree.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/pycdc.dir/ASTree.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/projs/langgraph-api-js/pycdc/ASTree.cpp -o CMakeFiles/pycdc.dir/ASTree.cpp.s

CMakeFiles/pycdc.dir/ASTNode.cpp.o: CMakeFiles/pycdc.dir/flags.make
CMakeFiles/pycdc.dir/ASTNode.cpp.o: ASTNode.cpp
CMakeFiles/pycdc.dir/ASTNode.cpp.o: CMakeFiles/pycdc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/pycdc.dir/ASTNode.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pycdc.dir/ASTNode.cpp.o -MF CMakeFiles/pycdc.dir/ASTNode.cpp.o.d -o CMakeFiles/pycdc.dir/ASTNode.cpp.o -c /Users/<USER>/projs/langgraph-api-js/pycdc/ASTNode.cpp

CMakeFiles/pycdc.dir/ASTNode.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/pycdc.dir/ASTNode.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/projs/langgraph-api-js/pycdc/ASTNode.cpp > CMakeFiles/pycdc.dir/ASTNode.cpp.i

CMakeFiles/pycdc.dir/ASTNode.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/pycdc.dir/ASTNode.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/projs/langgraph-api-js/pycdc/ASTNode.cpp -o CMakeFiles/pycdc.dir/ASTNode.cpp.s

# Object files for target pycdc
pycdc_OBJECTS = \
"CMakeFiles/pycdc.dir/pycdc.cpp.o" \
"CMakeFiles/pycdc.dir/ASTree.cpp.o" \
"CMakeFiles/pycdc.dir/ASTNode.cpp.o"

# External object files for target pycdc
pycdc_EXTERNAL_OBJECTS =

pycdc: CMakeFiles/pycdc.dir/pycdc.cpp.o
pycdc: CMakeFiles/pycdc.dir/ASTree.cpp.o
pycdc: CMakeFiles/pycdc.dir/ASTNode.cpp.o
pycdc: CMakeFiles/pycdc.dir/build.make
pycdc: libpycxx.a
pycdc: CMakeFiles/pycdc.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable pycdc"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/pycdc.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/pycdc.dir/build: pycdc
.PHONY : CMakeFiles/pycdc.dir/build

CMakeFiles/pycdc.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pycdc.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pycdc.dir/clean

CMakeFiles/pycdc.dir/depend:
	cd /Users/<USER>/projs/langgraph-api-js/pycdc && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/pycdc.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/pycdc.dir/depend

