# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/projs/langgraph-api-js/pycdc

# Include any dependencies generated for this target.
include CMakeFiles/pycdas.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/pycdas.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pycdas.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/pycdas.dir/flags.make

CMakeFiles/pycdas.dir/codegen:
.PHONY : CMakeFiles/pycdas.dir/codegen

CMakeFiles/pycdas.dir/pycdas.cpp.o: CMakeFiles/pycdas.dir/flags.make
CMakeFiles/pycdas.dir/pycdas.cpp.o: pycdas.cpp
CMakeFiles/pycdas.dir/pycdas.cpp.o: CMakeFiles/pycdas.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/pycdas.dir/pycdas.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pycdas.dir/pycdas.cpp.o -MF CMakeFiles/pycdas.dir/pycdas.cpp.o.d -o CMakeFiles/pycdas.dir/pycdas.cpp.o -c /Users/<USER>/projs/langgraph-api-js/pycdc/pycdas.cpp

CMakeFiles/pycdas.dir/pycdas.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/pycdas.dir/pycdas.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/projs/langgraph-api-js/pycdc/pycdas.cpp > CMakeFiles/pycdas.dir/pycdas.cpp.i

CMakeFiles/pycdas.dir/pycdas.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/pycdas.dir/pycdas.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/projs/langgraph-api-js/pycdc/pycdas.cpp -o CMakeFiles/pycdas.dir/pycdas.cpp.s

# Object files for target pycdas
pycdas_OBJECTS = \
"CMakeFiles/pycdas.dir/pycdas.cpp.o"

# External object files for target pycdas
pycdas_EXTERNAL_OBJECTS =

pycdas: CMakeFiles/pycdas.dir/pycdas.cpp.o
pycdas: CMakeFiles/pycdas.dir/build.make
pycdas: libpycxx.a
pycdas: CMakeFiles/pycdas.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable pycdas"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/pycdas.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/pycdas.dir/build: pycdas
.PHONY : CMakeFiles/pycdas.dir/build

CMakeFiles/pycdas.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pycdas.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pycdas.dir/clean

CMakeFiles/pycdas.dir/depend:
	cd /Users/<USER>/projs/langgraph-api-js/pycdc && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc /Users/<USER>/projs/langgraph-api-js/pycdc/CMakeFiles/pycdas.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/pycdas.dir/depend

