cookie = 1 <EOL>
constant_headers_1 = { 'Accept' : 'application/json' , 'Accept-Encoding' : 'gzip, deflate, br' , 'Accept-Language' : 'es,ca;q=0.9,en;q=0.8' , 'Cache-Control' : 'no-cache' , 'Connection' : 'keep-alive' , 'Content-Type' : 'application/x-www-form-urlencoded' , 'Pragma' : 'no-cache' , 'User-Agent' : 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36' , 'Cookie' : cookie } <EOL>
constant_headers_2 = { 'Accept' : 'application/json' , 'Accept-Encoding' : 'gzip, deflate, br' , 'Accept-Language' : 'es,ca;q=0.9,en;q=0.8' , 'Cache-Control' : 'no-cache' , 'Connection' : 'keep-alive' , 'Content-Type' : 'application/x-www-form-urlencoded' , 'Pragma' : 'no-cache' , 'User-Agent' : 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36' , 'Cookie' : 'constant cookie' } <EOL>
