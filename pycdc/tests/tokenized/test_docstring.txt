'\nModule docstring\n' <EOL>
def Doc_Test ( ) : <EOL>
<INDENT>
'Function docstring' <EOL>
pass <EOL>
<OUTDENT>
class XXX : <EOL>
<INDENT>
'Class docstring' <EOL>
def __init__ ( self ) : <EOL>
<INDENT>
'__init__:  Member function docstring' <EOL>
self . a = 1 <EOL>
def XXX22 ( ) : <EOL>
<INDENT>
'XXX22: Nested function docstring' <EOL>
pass <EOL>
<OUTDENT>
<OUTDENT>
def XXX11 ( ) : <EOL>
<INDENT>
'XXX11: Member Function docstring' <EOL>
pass <EOL>
<OUTDENT>
def XXX12 ( ) : <EOL>
<INDENT>
foo = 'XXX12: Normal string' <EOL>
<OUTDENT>
def XXX13 ( ) : <EOL>
<INDENT>
pass <EOL>
<OUTDENT>
<OUTDENT>
def Y11 ( ) : <EOL>
<INDENT>
def Y22 ( ) : <EOL>
<INDENT>
def Y33 ( ) : <EOL>
<INDENT>
pass <EOL>
<OUTDENT>
<OUTDENT>
<OUTDENT>
print __doc__ <EOL>
