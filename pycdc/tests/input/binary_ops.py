(a, b) = (10, 3)
print('Addition:', a + b)
print('Subtraction:', a - b)
print('Multiplication:', a * b)
print('Division:', a / b)
print('Modulo:', a % b)
print('Floor Division:', a // b)
print('Exponentiation:', a ** b)
print('Greater than:', a > b)
print('Less than:', a < b)
print('Greater than or equal to:', a >= b)
print('Less than or equal to:', a <= b)
print('Equal to:', a == b)
print('Not equal to:', a != b)
#print('Logical AND:', a and b)
#print('Logical OR:', a or b)
print('Bitwise AND:', a & b)
print('Bitwise OR:', a | b)
print('Bitwise XOR:', a ^ b)
print('Bitwise left shift:', a << b)
print('Bitwise right shift:', a >> b)
print('Membership in:', a in b)
print('Membership not in:', a not in b)
print('Identity equality:', a is b)
print('Identity inequality:', a is not b)
