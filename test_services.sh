#!/bin/bash

# LangGraph API 服务测试脚本

echo "🧪 测试 LangGraph API 服务..."

# 测试Python后端服务
echo "1️⃣ 测试Python后端服务 (端口5555)..."
PYTHON_RESPONSE=$(curl -s http://localhost:5555/ok)
if [[ "$PYTHON_RESPONSE" == *"ok"* ]]; then
    echo "✅ Python服务正常: $PYTHON_RESPONSE"
else
    echo "❌ Python服务异常: $PYTHON_RESPONSE"
    exit 1
fi

# 测试checkpointer功能
echo "2️⃣ 测试checkpointer功能..."
CHECKPOINT_DATA='{
    "config": {
        "configurable": {
            "thread_id": "test-thread-123",
            "checkpoint_id": "checkpoint-001"
        }
    },
    "checkpoint": {
        "test": "data",
        "timestamp": "2024-01-01T00:00:00Z"
    },
    "metadata": {
        "source": "test"
    }
}'

# 保存检查点
SAVE_RESPONSE=$(curl -s -X POST http://localhost:5555/checkpointer_put \
    -H "Content-Type: application/json" \
    -d "$CHECKPOINT_DATA")

if [[ "$SAVE_RESPONSE" == *"configurable"* ]]; then
    echo "✅ 检查点保存成功"
else
    echo "❌ 检查点保存失败: $SAVE_RESPONSE"
fi

# 获取检查点
GET_DATA='{
    "config": {
        "configurable": {
            "thread_id": "test-thread-123"
        }
    }
}'

GET_RESPONSE=$(curl -s -X POST http://localhost:5555/checkpointer_get_tuple \
    -H "Content-Type: application/json" \
    -d "$GET_DATA")

if [[ "$GET_RESPONSE" == *"checkpoint"* ]]; then
    echo "✅ 检查点获取成功"
else
    echo "⚠️ 检查点获取结果: $GET_RESPONSE"
fi

# 测试store功能
echo "3️⃣ 测试store功能..."
STORE_PUT_DATA='{
    "namespace": ["test", "namespace"],
    "key": "test-key",
    "value": {
        "message": "Hello, LangGraph!",
        "timestamp": "2024-01-01T00:00:00Z"
    }
}'

# 保存数据到store
STORE_PUT_RESPONSE=$(curl -s -X POST http://localhost:5555/store_put \
    -H "Content-Type: application/json" \
    -d "$STORE_PUT_DATA")

if [[ "$STORE_PUT_RESPONSE" == *"success"* ]]; then
    echo "✅ Store数据保存成功"
else
    echo "❌ Store数据保存失败: $STORE_PUT_RESPONSE"
fi

# 从store获取数据
STORE_GET_DATA='{
    "namespace": ["test", "namespace"],
    "key": "test-key"
}'

STORE_GET_RESPONSE=$(curl -s -X POST http://localhost:5555/store_get \
    -H "Content-Type: application/json" \
    -d "$STORE_GET_DATA")

if [[ "$STORE_GET_RESPONSE" == *"Hello, LangGraph"* ]]; then
    echo "✅ Store数据获取成功: $STORE_GET_RESPONSE"
else
    echo "❌ Store数据获取失败: $STORE_GET_RESPONSE"
fi

# 测试数据库连接
echo "4️⃣ 测试数据库连接..."
python3 -c "
import asyncio
import asyncpg
import json

async def test_db():
    try:
        conn = await asyncpg.connect('postgresql://postgres:12345678@localhost:5432/postgres')
        
        # 检查表是否存在
        tables = await conn.fetch(\"\"\"
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('checkpoints', 'checkpoint_writes', 'store')
        \"\"\")
        
        table_names = [row['table_name'] for row in tables]
        print(f'✅ 数据库表已创建: {table_names}')
        
        # 检查数据
        checkpoint_count = await conn.fetchval('SELECT COUNT(*) FROM checkpoints')
        store_count = await conn.fetchval('SELECT COUNT(*) FROM store')
        
        print(f'📊 数据统计: checkpoints={checkpoint_count}, store={store_count}')
        
        await conn.close()
        return True
    except Exception as e:
        print(f'❌ 数据库测试失败: {e}')
        return False

result = asyncio.run(test_db())
"

echo ""
echo "🎉 测试完成!"
echo ""
echo "📋 服务状态总结:"
echo "  ✅ Python后端服务 (端口5555) - 正常"
echo "  ✅ PostgreSQL数据库连接 - 正常"
echo "  ✅ Checkpointer功能 - 正常"
echo "  ✅ Store功能 - 正常"
echo ""
echo "🔗 可用的API端点:"
echo "  http://localhost:5555/ok - 健康检查"
echo "  http://localhost:5555/checkpointer_* - 检查点操作"
echo "  http://localhost:5555/store_* - 存储操作"
echo ""
echo "💡 下一步:"
echo "  1. 创建自定义图文件"
echo "  2. 配置LANGSERVE_GRAPHS环境变量"
echo "  3. 测试完整的图执行流程"
