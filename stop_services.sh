#!/bin/bash

# LangGraph API 服务停止脚本

echo "🛑 停止 LangGraph API 服务..."

# 从PID文件读取进程ID
if [ -f .service_pids ]; then
    PIDS=$(cat .service_pids)
    echo "发现运行中的服务PID: $PIDS"
    
    for PID in $PIDS; do
        if kill -0 $PID 2>/dev/null; then
            echo "停止进程 $PID..."
            kill $PID
        else
            echo "进程 $PID 已经停止"
        fi
    done
    
    # 等待进程停止
    sleep 2
    
    # 强制停止仍在运行的进程
    for PID in $PIDS; do
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程 $PID..."
            kill -9 $PID
        fi
    done
    
    rm -f .service_pids
    echo "✅ 所有服务已停止"
else
    echo "未找到PID文件，尝试通过端口停止服务..."
    
    # 通过端口查找并停止进程
    for PORT in 5555 5556 8000; do
        PID=$(lsof -ti:$PORT 2>/dev/null)
        if [ ! -z "$PID" ]; then
            echo "停止端口 $PORT 上的进程 $PID..."
            kill $PID
        fi
    done
    
    echo "✅ 服务停止完成"
fi
