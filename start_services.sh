#!/bin/bash

# LangGraph API 服务启动脚本
# 支持PostgreSQL存储的完整启动方案

echo "🚀 启动 LangGraph API 服务..."

# 设置环境变量
export DATABASE_URL="postgresql://postgres:12345678@localhost:5432/postgres"
export LANGSERVE_GRAPHS='{"designToCode":"/Users/<USER>/projs/ht/langgraph-mvp-demo/dist/graph/designToCode/index.js:graph","simpleChat":"/Users/<USER>/projs/ht/langgraph-mvp-demo/dist/graph/simpleChat/index.js:graph"}'
export PORT=8000
export LANGGRAPH_HTTP='{"app": "/Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js"}'

echo "📊 环境变量配置:"
echo "  数据库连接: $DATABASE_URL"
echo "  图配置: $LANGSERVE_GRAPHS"
echo "  HTTP端口: $PORT"

# 检查PostgreSQL连接
echo "🔍 检查PostgreSQL连接..."
python3 -c "
import asyncpg
import asyncio

async def test_connection():
    try:
        conn = await asyncpg.connect('$DATABASE_URL')
        await conn.close()
        print('✅ PostgreSQL连接成功')
        return True
    except Exception as e:
        print(f'❌ PostgreSQL连接失败: {e}')
        return False

result = asyncio.run(test_connection())
exit(0 if result else 1)
"

if [ $? -ne 0 ]; then
    echo "❌ 数据库连接失败，请检查PostgreSQL服务是否运行"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动Python后端服务 (端口5555)
echo "🐍 启动Python后端服务 (端口5555)..."
python3 python_service.py > logs/python_service.log 2>&1 &
PYTHON_PID=$!
echo "Python服务PID: $PYTHON_PID"

# 等待Python服务启动
sleep 3

# 检查Python服务是否启动成功
if ! curl -s http://localhost:5555/ok > /dev/null; then
    echo "❌ Python服务启动失败"
    kill $PYTHON_PID 2>/dev/null
    exit 1
fi
echo "✅ Python服务启动成功"

# 启动Node.js图服务 (端口5556)
echo "📊 启动Node.js图服务 (端口5556)..."
npx tsx client.mts > logs/graph_service.log 2>&1 &
GRAPH_PID=$!
echo "图服务PID: $GRAPH_PID"

# 启动Node.js HTTP服务 (端口8000)
echo "🌐 启动Node.js HTTP服务 (端口$PORT)..."
npx tsx client.http.mts > logs/http_service.log 2>&1 &
HTTP_PID=$!
echo "HTTP服务PID: $HTTP_PID"

# 等待服务启动
sleep 5

echo ""
echo "🎉 LangGraph API 服务启动完成!"
echo ""
echo "📋 服务状态:"
echo "  Python后端服务: http://localhost:5555 (PID: $PYTHON_PID)"
echo "  Node.js图服务: http://localhost:5556 (PID: $GRAPH_PID)"
echo "  HTTP API服务: http://localhost:$PORT (PID: $HTTP_PID)"
echo ""
echo "📁 日志文件:"
echo "  Python服务: logs/python_service.log"
echo "  图服务: logs/graph_service.log"
echo "  HTTP服务: logs/http_service.log"
echo ""
echo "🛑 停止服务命令:"
echo "  kill $PYTHON_PID $GRAPH_PID $HTTP_PID"
echo ""
echo "💡 测试命令:"
echo "  curl http://localhost:5555/ok  # 测试Python服务"
echo "  curl http://localhost:$PORT/health  # 测试HTTP服务"

# 保存PID到文件，方便后续停止
echo "$PYTHON_PID $GRAPH_PID $HTTP_PID" > .service_pids

# 等待用户中断
echo "按 Ctrl+C 停止所有服务..."
trap 'echo ""; echo "🛑 停止所有服务..."; kill $PYTHON_PID $GRAPH_PID $HTTP_PID 2>/dev/null; rm -f .service_pids; echo "✅ 所有服务已停止"; exit 0' INT

# 保持脚本运行
wait
