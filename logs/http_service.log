{"timestamp":"2025-08-26T09:04:18.818Z","level":"info","event":"Starting HTTP loop","pid":57240}
{"timestamp":"2025-08-26T09:04:18.844Z","level":"error","event":"Error: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)","error":{},"stack":"Error: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)","rejection":true,"date":"Tue Aug 26 2025 17:04:18 GMT+0800 (China Standard Time)","process":{"pid":57240,"uid":501,"gid":20,"cwd":"/Users/<USER>/projs/langgraph-api-js","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","version":"v20.19.0","argv":["/Users/<USER>/.nvm/versions/node/v20.19.0/bin/node","/Users/<USER>/projs/langgraph-api-js/client.http.mts"],"memoryUsage":{"rss":104153088,"heapTotal":19988480,"heapUsed":12094816,"external":3885414,"arrayBuffers":74178}},"os":{"loadavg":[2.6923828125,3.40185546875,3.11669921875],"uptime":15559},"trace":[{"column":20,"file":"/Users/<USER>/projs/langgraph-api-js/client.http.mts","function":"registerHttp","line":102,"method":null,"native":false},{"column":15,"file":"/Users/<USER>/projs/langgraph-api-js/client.http.mts","function":"async main","line":138,"method":null,"native":false}],"message":"unhandledRejection: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\nError: Failed to load HTTP app: /Users/<USER>/projs/ht/langgraph-mvp-demo/dist/routes/app.js\n    at registerHttp (/Users/<USER>/projs/langgraph-api-js/client.http.mts:102:20)\n    at async main (/Users/<USER>/projs/langgraph-api-js/client.http.mts:138:15)"}
