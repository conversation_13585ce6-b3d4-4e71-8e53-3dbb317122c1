# LangGraph API with PostgreSQL Support

本项目提供了一个完整的LangGraph API服务，支持PostgreSQL数据库存储。

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP Client   │───▶│  Node.js HTTP   │───▶│  Node.js Graph  │
│                 │    │   Service       │    │    Service      │
│                 │    │   (Port 8000)   │    │   (Port 5556)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Python Backend │    │   PostgreSQL    │
                       │   Service       │───▶│    Database     │
                       │   (Port 5555)   │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## 📋 系统要求

- Node.js 18+
- Python 3.9+
- PostgreSQL 12+
- npm/yarn

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Node.js依赖
npm install
# 或
yarn install

# 安装Python依赖
pip3 install -r requirements.txt
```

### 2. 配置数据库

确保PostgreSQL服务正在运行，并且可以使用以下连接信息：

```
Host: localhost
Port: 5432
Database: postgres
Username: postgres
Password: 12345678
```

### 3. 启动服务

使用提供的启动脚本：

```bash
./start_services.sh
```

或者手动启动各个服务：

```bash
# 1. 启动Python后端服务
export DATABASE_URL="postgresql://postgres:12345678@localhost:5432/postgres"
python3 python_service.py &

# 2. 启动Node.js图服务
export LANGSERVE_GRAPHS='{"chat": "src/graphs/chat.ts:graph"}'
npx tsx client.mts &

# 3. 启动HTTP服务
export PORT=8000
npx tsx client.http.mts &
```

### 4. 验证服务

```bash
# 测试Python后端服务
curl http://localhost:5555/ok

# 测试HTTP服务
curl http://localhost:8000/health
```

## 🔧 服务端口

| 服务 | 端口 | 描述 |
|------|------|------|
| Python Backend | 5555 | 处理checkpointer和store操作 |
| Node.js Graph | 5556 | 图执行服务 |
| HTTP API | 8000 | 对外HTTP接口 |

## 💾 数据库支持

### 自动创建的表结构

Python服务启动时会自动创建以下表：

1. **checkpoints** - 存储图的检查点数据
   ```sql
   CREATE TABLE checkpoints (
       thread_id TEXT NOT NULL,
       checkpoint_ns TEXT NOT NULL DEFAULT '',
       checkpoint_id TEXT NOT NULL,
       parent_checkpoint_id TEXT,
       type TEXT,
       checkpoint JSONB NOT NULL,
       metadata JSONB NOT NULL DEFAULT '{}',
       PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id)
   );
   ```

2. **checkpoint_writes** - 存储检查点写入操作
   ```sql
   CREATE TABLE checkpoint_writes (
       thread_id TEXT NOT NULL,
       checkpoint_ns TEXT NOT NULL DEFAULT '',
       checkpoint_id TEXT NOT NULL,
       task_id TEXT NOT NULL,
       idx INTEGER NOT NULL,
       channel TEXT NOT NULL,
       type TEXT,
       value JSONB,
       PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id, task_id, idx)
   );
   ```

3. **store** - 存储键值对数据
   ```sql
   CREATE TABLE store (
       prefix TEXT[] NOT NULL,
       key TEXT NOT NULL,
       value JSONB NOT NULL,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       PRIMARY KEY (prefix, key)
   );
   ```

### 支持的操作

- ✅ 检查点保存和恢复
- ✅ 图状态持久化
- ✅ 键值存储
- ✅ 事务支持
- ✅ 并发访问

## 🔌 API 端点

### Python Backend API (端口5555)

- `GET /ok` - 健康检查
- `POST /checkpointer_get_tuple` - 获取检查点
- `POST /checkpointer_put` - 保存检查点
- `POST /checkpointer_put_writes` - 保存检查点写入
- `POST /store_get` - 获取存储数据
- `POST /store_put` - 保存存储数据

### HTTP API (端口8000)

- `GET /health` - 健康检查
- `POST /invoke` - 调用图
- `POST /stream` - 流式调用图
- `GET /graph` - 获取图结构

## 📝 使用示例

### 创建自定义图

1. 在 `src/graphs/` 目录下创建新的图文件：

```typescript
// src/graphs/my_graph.ts
import { StateGraph } from "@langchain/langgraph";

interface MyState {
  input: string;
  output: string;
}

const graph = new StateGraph<MyState>({
  channels: {
    input: { value: (x, y) => y ?? x, default: () => "" },
    output: { value: (x, y) => y ?? x, default: () => "" },
  },
});

// 添加节点和逻辑...

export const compiledGraph = graph.compile();
export default compiledGraph;
```

2. 更新环境变量：

```bash
export LANGSERVE_GRAPHS='{"my_graph": "src/graphs/my_graph.ts:compiledGraph"}'
```

### 调用图API

```bash
# 调用图
curl -X POST http://localhost:8000/invoke \
  -H "Content-Type: application/json" \
  -d '{
    "input": {"user_input": "Hello, world!"},
    "config": {"configurable": {"thread_id": "test-thread"}}
  }'
```

## 🛑 停止服务

```bash
# 使用停止脚本
./stop_services.sh

# 或手动停止
kill $(cat .service_pids)
```

## 📊 监控和日志

服务启动后，日志文件位于 `logs/` 目录：

- `logs/python_service.log` - Python后端服务日志
- `logs/graph_service.log` - Node.js图服务日志
- `logs/http_service.log` - HTTP服务日志

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `DATABASE_URL` | `postgresql://postgres:12345678@localhost:5432/postgres` | PostgreSQL连接字符串 |
| `LANGSERVE_GRAPHS` | `{"chat": "src/graphs/chat.ts:graph"}` | 图配置 |
| `PORT` | `8000` | HTTP服务端口 |

### 数据库配置

可以通过修改 `DATABASE_URL` 环境变量来配置不同的数据库连接：

```bash
export DATABASE_URL="postgresql://username:password@host:port/database"
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否运行
   - 验证连接参数是否正确
   - 确保数据库用户有足够权限

2. **端口冲突**
   - 检查端口5555、5556、8000是否被占用
   - 使用 `lsof -i :PORT` 查看端口使用情况

3. **Python依赖问题**
   - 确保所有Python包都已正确安装
   - 检查Python版本是否兼容

### 调试模式

启用详细日志：

```bash
export LOG_LEVEL=DEBUG
./start_services.sh
```

## 📚 更多资源

- [LangGraph 官方文档](https://langchain-ai.github.io/langgraph/)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [PostgreSQL 文档](https://www.postgresql.org/docs/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
