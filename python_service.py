#!/usr/bin/env python3
"""
简化的LangGraph Python服务
基于反编译代码重新实现，支持PostgreSQL存储
"""

import os
import asyncio
import logging
from typing import Any, Dict, List, Optional
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import orjson
import asyncpg

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 端口配置（与原代码保持一致）
REMOTE_PORT = 5555
GRAPH_PORT = 5556
GRAPH_HTTP_PORT = 5557

# 数据库连接池
db_pool: Optional[asyncpg.Pool] = None

class CheckpointerRequest(BaseModel):
    config: Dict[str, Any]
    checkpoint: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    new_versions: Optional[Dict[str, Any]] = None

class StoreRequest(BaseModel):
    namespace: Optional[List[str]] = None
    key: Optional[str] = None
    value: Optional[Dict[str, Any]] = None
    operations: Optional[List[Dict[str, Any]]] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global db_pool
    
    # 启动时初始化数据库连接池
    database_url = os.getenv("DATABASE_URL", "postgresql://postgres:12345678@localhost:5432/postgres")
    logger.info(f"连接数据库: {database_url}")
    
    try:
        db_pool = await asyncpg.create_pool(
            database_url,
            min_size=1,
            max_size=10,
            command_timeout=60
        )
        logger.info("数据库连接池创建成功")
        
        # 初始化数据库表结构
        await init_database_tables()
        
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise
    
    yield
    
    # 关闭时清理资源
    if db_pool:
        await db_pool.close()
        logger.info("数据库连接池已关闭")

async def init_database_tables():
    """初始化数据库表结构"""
    if not db_pool:
        return
        
    async with db_pool.acquire() as conn:
        # 创建checkpoints表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS checkpoints (
                thread_id TEXT NOT NULL,
                checkpoint_ns TEXT NOT NULL DEFAULT '',
                checkpoint_id TEXT NOT NULL,
                parent_checkpoint_id TEXT,
                type TEXT,
                checkpoint JSONB NOT NULL,
                metadata JSONB NOT NULL DEFAULT '{}',
                PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id)
            )
        """)
        
        # 创建checkpoint_writes表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS checkpoint_writes (
                thread_id TEXT NOT NULL,
                checkpoint_ns TEXT NOT NULL DEFAULT '',
                checkpoint_id TEXT NOT NULL,
                task_id TEXT NOT NULL,
                idx INTEGER NOT NULL,
                channel TEXT NOT NULL,
                type TEXT,
                value JSONB,
                PRIMARY KEY (thread_id, checkpoint_ns, checkpoint_id, task_id, idx)
            )
        """)
        
        # 创建store表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS store (
                prefix TEXT[] NOT NULL,
                key TEXT NOT NULL,
                value JSONB NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                PRIMARY KEY (prefix, key)
            )
        """)
        
        logger.info("数据库表结构初始化完成")

# 创建FastAPI应用
app = FastAPI(
    title="LangGraph Remote Service",
    description="LangGraph远程服务，支持PostgreSQL存储",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/ok")
async def health_check():
    """健康检查端点"""
    return {"ok": True}

@app.post("/checkpointer_get_tuple")
async def checkpointer_get_tuple(request: CheckpointerRequest):
    """获取检查点元组"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="数据库连接未初始化")
    
    config = request.config
    thread_id = config.get("configurable", {}).get("thread_id")
    checkpoint_ns = config.get("configurable", {}).get("checkpoint_ns", "")
    
    if not thread_id:
        return None
    
    async with db_pool.acquire() as conn:
        # 获取最新的检查点
        row = await conn.fetchrow("""
            SELECT checkpoint_id, parent_checkpoint_id, type, checkpoint, metadata
            FROM checkpoints 
            WHERE thread_id = $1 AND checkpoint_ns = $2
            ORDER BY checkpoint_id DESC 
            LIMIT 1
        """, thread_id, checkpoint_ns)
        
        if not row:
            return None
            
        return {
            "config": {
                **config,
                "configurable": {
                    **config.get("configurable", {}),
                    "checkpoint_id": row["checkpoint_id"]
                }
            },
            "checkpoint": row["checkpoint"],
            "metadata": row["metadata"],
            "parent_config": {
                **config,
                "configurable": {
                    **config.get("configurable", {}),
                    "checkpoint_id": row["parent_checkpoint_id"]
                }
            } if row["parent_checkpoint_id"] else None
        }

@app.post("/checkpointer_put")
async def checkpointer_put(request: CheckpointerRequest):
    """保存检查点"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="数据库连接未初始化")
    
    config = request.config
    checkpoint = request.checkpoint
    metadata = request.metadata or {}
    
    thread_id = config.get("configurable", {}).get("thread_id")
    checkpoint_ns = config.get("configurable", {}).get("checkpoint_ns", "")
    checkpoint_id = config.get("configurable", {}).get("checkpoint_id")
    
    if not all([thread_id, checkpoint_id, checkpoint]):
        raise HTTPException(status_code=400, detail="缺少必需参数")
    
    async with db_pool.acquire() as conn:
        await conn.execute("""
            INSERT INTO checkpoints (thread_id, checkpoint_ns, checkpoint_id, checkpoint, metadata)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (thread_id, checkpoint_ns, checkpoint_id) 
            DO UPDATE SET checkpoint = $4, metadata = $5
        """, thread_id, checkpoint_ns, checkpoint_id, 
            orjson.dumps(checkpoint).decode(), orjson.dumps(metadata).decode())
    
    return config

@app.post("/checkpointer_put_writes")
async def checkpointer_put_writes(request: CheckpointerRequest):
    """保存检查点写入"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="数据库连接未初始化")
    
    config = request.config
    writes = request.checkpoint  # 这里复用checkpoint字段传递writes
    task_id = request.metadata.get("task_id") if request.metadata else None
    
    thread_id = config.get("configurable", {}).get("thread_id")
    checkpoint_ns = config.get("configurable", {}).get("checkpoint_ns", "")
    checkpoint_id = config.get("configurable", {}).get("checkpoint_id")
    
    if not all([thread_id, checkpoint_id, writes, task_id]):
        raise HTTPException(status_code=400, detail="缺少必需参数")
    
    async with db_pool.acquire() as conn:
        for idx, (channel, value) in enumerate(writes):
            await conn.execute("""
                INSERT INTO checkpoint_writes 
                (thread_id, checkpoint_ns, checkpoint_id, task_id, idx, channel, value)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """, thread_id, checkpoint_ns, checkpoint_id, task_id, idx, channel,
                orjson.dumps(value).decode())
    
    return {"success": True}

@app.post("/store_get")
async def store_get(request: StoreRequest):
    """获取存储数据"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="数据库连接未初始化")
    
    namespace = request.namespace or []
    key = request.key
    
    if not key:
        raise HTTPException(status_code=400, detail="缺少key参数")
    
    async with db_pool.acquire() as conn:
        row = await conn.fetchrow("""
            SELECT value FROM store WHERE prefix = $1 AND key = $2
        """, namespace, key)
        
        if not row:
            return None
            
        return orjson.loads(row["value"])

@app.post("/store_put")
async def store_put(request: StoreRequest):
    """保存存储数据"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="数据库连接未初始化")
    
    namespace = request.namespace or []
    key = request.key
    value = request.value
    
    if not key or value is None:
        raise HTTPException(status_code=400, detail="缺少key或value参数")
    
    async with db_pool.acquire() as conn:
        await conn.execute("""
            INSERT INTO store (prefix, key, value, updated_at)
            VALUES ($1, $2, $3, NOW())
            ON CONFLICT (prefix, key) 
            DO UPDATE SET value = $3, updated_at = NOW()
        """, namespace, key, orjson.dumps(value).decode())
    
    return {"success": True}

if __name__ == "__main__":
    # 设置数据库连接字符串
    database_url = os.getenv("DATABASE_URL", "postgresql://postgres:12345678@localhost:5432/postgres")
    print(f"启动Python服务，数据库连接: {database_url}")
    print(f"服务端口: {REMOTE_PORT}")
    
    uvicorn.run(
        "python_service:app",
        host="0.0.0.0",
        port=REMOTE_PORT,
        log_level="info",
        reload=False
    )
