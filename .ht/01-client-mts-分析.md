### 概览

`client.mts` 实现了一个基于 Hono 的本地 HTTP 服务（默认监听 `localhost:5556`），对外暴露与 LangGraph 运行时交互的一组 API（查询图、流式事件、状态读写、历史、Schema、子图等）。该文件同时封装了远端 Store 与 Checkpointer 的代理、日志、心跳与错误处理、可选鉴权、以及图 Schema 的缓存加载与静态解析流程。

---

### 关键依赖与模块职责

- **Web 框架与服务**: `hono`, `@hono/node-server`, `@hono/zod-validator`, `hono/streaming`
  - 提供 HTTP 路由、请求校验（Zod）、流式输出（SSE/Chunked）。
- **LangGraph 核心**: `@langchain/langgraph`, `@langchain/core/*`
  - 图的编译与执行、消息与回调、可运行配置 `RunnableConfig`。
- **Schema 提取**: `@langchain/langgraph-api/schema`
  - `getRuntimeGraphSchema`, `getStaticGraphSchema` 实现运行时/静态 Schema 的拉取/解析。
- **鉴权**: `@langchain/langgraph-api/auth`
  - 可选注册 `authenticate`, `authorize`，由 `LANGGRAPH_AUTH` 配置控制。
- **远端资源代理**: `RemoteCheckpointer`, `RemoteStore`
  - 通过本地 5555 端口的“遥控”接口把 Checkpointer/Store 操作转发到 Python/远端实现。
- **工具与辅助**: `winston` 日志、`p-retry` 带重试的 `tryFetch`、`serde` 序列化、`importMap` 动态加载。
- **Graph 装载与解析**: `resolveGraph` 从导出路径载入图定义，支持工厂或编译后的图。

---

### 全局状态与常量

- `GRAPH_SCHEMA: Record<string, Record<string, GraphSchema> | false>`
  - 运行期缓存已解析的 Graph Schema；构建期失败会标记为 `false`。
- `GRAPH_OPTIONS: { checkpointer?: BaseCheckpointSaver; store?: BaseStore }`
  - 初始化时注入远端 Checkpointer/Store。
- `GRAPH_RESOLVED: Record<string, CompiledGraph | CompiledGraphFactory>` 与 `GRAPH_SPEC: Record<string, GraphSpec>`
  - 存放每个 `graphId` 的已解析图/图规格。
- `GRAPH_PORT=5556`, `REMOTE_PORT=5555`
  - 本地 API 暴露端口与远端资源端口。
- `HEARTBEAT_MS=5000`
  - 流式/长连接的心跳间隔。
- `nodesExecuted` 与 `incrementNodes()`
  - 通过在 `config.configurable.__pregel_node_finished` 注入回调统计节点执行次数，并提供重置读取端点。

---

### 日志体系

- 基于 `winston` 创建 `logger`，使用自定义 `injectConfigFormatter` 注入 `langgraph_node` 元数据，输出 JSON 格式日志，捕获异常/未捕获错误。
- 通过全局符号 `GLOBAL_LOGGER` 暴露到 `globalThis`，供其他模块读取。

---

### 版本与环境变量

- 动态解析 `@langchain/langgraph/package.json` 的版本，并在执行时注入到 `config.metadata.langgraph_version`。
- 环境变量：
  - `LANGSERVE_GRAPHS`: JSON，定义需要加载的图列表（`graphId -> path | {path, description}`）。
  - `LANGGRAPH_SCHEMA_RESOLVE_TIMEOUT_MS`: 解析静态 Schema 的超时毫秒数（默认 30000）。
  - `LANGGRAPH_AUTH`: 可选鉴权配置（`{ path?: string, disable_studio_auth?: boolean }`）。

---

### Graph 装载与获取

- `resolveGraph(importPath)`
  - 返回 `{ resolved, ...spec }`，其中 `resolved` 为编译后的图或工厂；`spec` 存入 `GRAPH_SPEC`。
- `getGraph(graphId, config, name)`
  - 根据 `graphId` 取出已解析图或执行工厂函数；并在存在时注入全局 `checkpointer`/`store`；覆盖默认 `name`。
- `getOrExtractSchema(graphId)`
  - 若 `GRAPH_SCHEMA[graphId]` 未初始化：
    - 优先读取缓存文件 `client.schemas.json`（在 `main()` 的预加载阶段尝试读取，除非 CLI 启动包含 `--skip-schema-cache`）。
    - 否则调用 `getStaticGraphSchema(GRAPH_SPEC[graphId], { timeoutMs })` 进行静态提取并缓存。

---

### 远端代理层（Store 与 Checkpointer）

#### 通用通信 `sendRecv`

- 通过 `tryFetch` 向 `http://localhost:5555/<method>` POST JSON，再用 `load` 反序列化（配合自定义的 `importMap`）。
- `tryFetch` 使用 `p-retry` 增强重试和错误信息。

#### `RemoteCheckpointer`

- 覆盖 `getTuple`, `list`, `put`, `putWrites`, `getNextVersion` 等：
  - 混合字符串/数字版本号处理，生成 32 位递增版本+16位随机哈希的 `nextVersion`。

#### `RemoteStore`

- `batch(operations)`：
  - 发送前使用 `camelToSnake` 转换字段至下划线风格；
  - 结果中若项为 `PyItem`，转换为 JavaScript 风格 `Item`（`created_at -> createdAt` 等）。
- `get`, `search`, `put`, `delete`, `listNamespaces` 均通过 `sendRecv` 转发。

---

### 流/请求处理器与协议

#### 通用封装

- `handleInvoke(name, schema, handler)`
  - 校验 JSON、返回单响应，使用 `stream` 包装并维持心跳（空格字节），以避免超时。
- `handleStream(name, schema, handler)`
  - SSE 输出；实现周期心跳（`: heartbeat` 注释行），并在每条事件后刷新 `timer`；异常时发送 `error` 事件。

#### 事件流细节 `streamEventsRequest`

- 载入图与 `RunnableConfig`，将 `command` 反序列化为 `Command`：
  - `goto` 可为 `Send|string|(Send|string)[]`，内部统一数组并转 `Send`；
  - `update`, `resume`, `graph` 原样透传。
- 将 `__pregel_node_finished` 注入 `config.configurable` 统计节点。
- 计算 `graphStreamMode`：
  - 支持用户扩展模式 `messages`, `messages-tuple` 映射到底层 `values`/`messages`；
  - 根据 `payload.subgraphs` 修正 chunk 结构（在只选 1 模式时确保 `data.data.chunk` 为 `[mode, chunk]`，且开启子图时前置空数组层）。
- 事件派发增强：
  - 若用户请求 `messages`：
    - 聚合 `on_chat_model_stream` 的消息分片，生成自定义事件 `messages/partial` 与对应 `messages/metadata`；
    - 在 `on_chain_stream` 检测新完整消息，触发 `messages/complete`。
- `interruptBefore/After` 支持 `string[] | "*" | undefined`，空数组时置为 `undefined`。

注意：`streamEvents` 调用处对 `streamMode` 的类型兼容性依赖 `@langchain/langgraph` 版本定义，若本地类型不含 `"tasks"` 等字面量，会在构建时报类型错误（见下文“已知类型告警”）。

---

### 其余请求处理逻辑

- `getGraphRequest`：返回 `graph.getGraphAsync(...).toJSON()` 的图形化结构（支持 `xray`）。
- `getSubgraphsRequest`：遍历 `graph.getSubgraphsAsync(ns?, recurse?)`，为每个子图优先返回运行时 Schema，否则回退至静态 Schema；若找不到根图 ID 抛错。
- `getStateRequest`：`graph.getState(config, { subgraphs })`，并通过 `serialiseAsDict`/`JSON.parse` 规整为 JSON。
- `updateStateRequest`：调用 `graph.updateState(config, values, as_node?)`，返回新的 `config`。
- `getStateHistoryRequest`（SSE）：拉取状态历史（支持 `limit`/`before`/`filter`）。
- `getSchemaRequest`：优先运行时 Schema，否则静态 Schema 根；找不到根图则报错。
- `getNodesExecutedRequest`：读取并清零 `nodesExecuted`，用于监控每次请求期间节点执行数。

---

### 路由与启动流程

1. 构造 `app = new Hono()`，注入 `GRAPH_OPTIONS` 为远端代理实例。
2. 解析 `LANGSERVE_GRAPHS`，过滤导出路径合法性后批量 `resolveGraph` 并填充 `GRAPH_RESOLVED`/`GRAPH_SPEC`。
3. 可选预读 `client.schemas.json` 到 `GRAPH_SCHEMA`（除非 `--skip-schema-cache`）。
4. 注册路由：
   - `POST /:graphId/streamEvents`（SSE）
   - `POST /:graphId/getGraph`
   - `POST /:graphId/getSubgraphs`
   - `POST /:graphId/getState`
   - `POST /:graphId/updateState`
   - `POST /:graphId/getSchema`
   - `POST /:graphId/getStateHistory`（SSE）
   - `POST /:graphId/getNodesExecuted`
   - `GET /ok` 健康检查
   - `GET /debug/heapdump` 触发 V8 堆快照
5. 鉴权（可选）：若 `LANGGRAPH_AUTH.path` 有效，注册 `/auth/authenticate` 与 `/auth/authorize` 两个端点，透传/处理上游抛出的 `HTTPException`。
6. 全局错误处理：
   - 401 透传 `HTTPException` 的 Response；
   - 其他错误转化为 `serializeError(err).message` 文本 500。
7. 启动监听 `localhost:5556`，并输出监听地址日志。
8. 进程钩子：
   - `process.on("uncaughtExceptionMonitor")` 记录并优雅退出；
   - `asyncExitHook(() => awaitAllCallbacks(), { wait: 3000 })`；
   - `patchFetch()` 对 `fetch` 进行观测/追踪增强。

---

### 数据模型与校验 Schema（Zod）

- `RunnableConfigSchema`：镜像 `RunnableConfig` 的用户输入形式（`run_name`, `max_concurrency`, `recursion_limit`, `configurable`, `tags`, `metadata`, `run_id`）。
- `StreamModeSchema` 与 `ExtraStreamModeSchema`：定义流模式以及扩展模式（`messages`, `messages-tuple`）。
- `StreamEventsPayload`：`graph_id`、`graph_name`、`graph_config`、`input`、`command`、`stream_mode`、`config`、`interrupt_before/after`、`subgraphs`。
- 其他：`GetGraphPayload`、`GetSubgraphsPayload`、`GetStatePayload`、`UpdateStatePayload`、`GetSchemaPayload`、`GetStateHistoryPayload`、`GetNodesExecutedPayload`。

---

### 错误处理与重试/心跳机制

- `tryFetch`：
  - 3 次指数退避重试，失败时尽量附带响应体文本。
- `handleInvoke`：
  - 使用 `stream` 返回；在计算期间每 `HEARTBEAT_MS` 向客户端写入空格保持连接；完成后写入完整 JSON。
- `handleStream`：
  - 使用 SSE 并周期写入 `: heartbeat`；异常写入 `event: error`，并记录堆栈到日志。

---

### 已知类型告警（构建期）

- 在 `graph.streamEvents(input, { ...config, version: "v2", streamMode, ... })` 处，若 `@langchain/langgraph` 的 `StreamMode` 类型未包含 `"tasks"` 等字面量，将触发类型错误（见 linter 报告）。
  - 解决思路：
    - 升级 `@langchain/langgraph` 到包含这些模式的版本；或
    - 对 `streamMode` 做窄化/映射，过滤掉不被当前版本类型接受的字面量；或
    - 在最小范围内添加类型断言并配套运行时守卫，防止不兼容模式透传。

---

### 安全与鉴权

- 若配置了 `LANGGRAPH_AUTH.path` 且路径合法，会调用 `registerAuth` 注册鉴权：
  - `/auth/authenticate`：从请求头提取上游 URL 与 Method，透传其余 Header；调用 `authenticate` 后返回鉴权上下文；
  - `/auth/authorize`：读取请求 JSON 调用 `authorize`；
  - 异常处理：优先输出结构化错误、状态码与（若有）上游响应头。

---

### 性能与可观测性要点

- 通过心跳保持长连接，避免代理/网关超时。
- `nodesExecuted` 提供轻量执行量度量接口；
- `writeHeapSnapshot` 提供在线堆快照抓取能力，辅助内存问题诊断；
- 日志包含 `langgraph_node` 等上下文，便于跨组件关联追踪。

---

### 典型调用序列（示例伪代码）

```bash
curl -X POST http://localhost:5556/<graphId>/streamEvents \
  -H 'Content-Type: application/json' \
  -d '{
    "graph_id": "<graphId>",
    "input": { ... },
    "stream_mode": ["updates", "messages"],
    "config": { "run_id": "...", "max_concurrency": 8 }
  }'
```

```bash
curl -X POST http://localhost:5556/<graphId>/getState \
  -H 'Content-Type: application/json' \
  -d '{
    "graph_id": "<graphId>",
    "config": { "run_id": "..." },
    "subgraphs": true
  }'
```

---

### 小结

`client.mts` 将 LangGraph 的执行能力通过本地 HTTP API 进行标准化包装：
- 对上屏蔽远端存储/检查点实现细节；
- 提供一致、类型校验的请求契约；
- 支持稳定的流式传输与心跳；
- 可插拔的鉴权；
- 运行时/静态 Schema 的双路径获取；
- 具备监控与诊断的最小闭环（日志、堆快照、节点计数）。

