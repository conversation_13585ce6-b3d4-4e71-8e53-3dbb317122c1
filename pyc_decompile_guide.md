# Python .pyc文件反编译指南

## 工具安装

### 方法1: 使用pycdc（推荐，支持Python 3.12）

```bash
# 1. 克隆源码
git clone https://github.com/zrax/pycdc.git
cd pycdc

# 2. 安装cmake（如果没有）
# macOS:
brew install cmake
# Ubuntu/Debian:
sudo apt-get install cmake build-essential
# CentOS/RHEL:
sudo yum install cmake gcc-c++

# 3. 编译
cmake CMakeLists.txt
make

# 4. 使用
./pycdc your_file.pyc
```

### 方法2: 使用uncompyle6（适用于Python 3.6-3.11）

```bash
# 安装
pip install uncompyle6

# 使用
uncompyle6 your_file.pyc
```

### 方法3: 使用decompyle3（适用于Python 3.6-3.11）

```bash
# 安装
pip install decompyle3

# 使用
decompyle3 your_file.pyc
```

## 批量反编译

### 反编译单个文件
```bash
./pycdc base.pyc > base.py
```

### 反编译多个文件
```bash
# 使用循环反编译所有pyc文件
for file in *.pyc; do
    echo "反编译 $file..."
    ./pycdc "$file" > "${file%.pyc}.py"
done
```

### 反编译整个目录
```bash
# 递归查找并反编译所有pyc文件
find . -name "*.pyc" -exec sh -c 'echo "反编译 {}..."; ./pycdc "{}" > "{}.py"' \;
```

## 注意事项

1. **版本兼容性**: 确保使用的反编译工具支持目标pyc文件的Python版本
2. **不完整反编译**: 某些复杂的字节码可能无法完全反编译
3. **代码质量**: 反编译的代码可能与原始代码在格式上有差异
4. **法律考虑**: 只反编译你有权限的代码

## 常见问题

### 错误: "Unsupported Python version"
- 使用支持该Python版本的工具
- 对于Python 3.12+，使用pycdc

### 错误: "Unsupported opcode"
- 某些新的字节码操作可能不被支持
- 尝试使用最新版本的反编译工具

### 部分反编译
- 这是正常现象，复杂的控制流可能无法完全恢复
- 可以结合字节码分析工具进一步分析

## 字节码分析

如果反编译失败，可以使用字节码分析：

```bash
# 使用pycdc的反汇编功能
./pycdas your_file.pyc

# 或使用Python内置工具
python -m dis your_file.pyc
```
