#!/bin/bash

# Python .pyc文件批量反编译脚本
# 使用pycdc工具反编译项目中的所有pyc文件

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查pycdc是否存在
PYCDC_PATH="./pycdc/pycdc"
if [ ! -f "$PYCDC_PATH" ]; then
    echo -e "${RED}错误: 找不到pycdc工具在 $PYCDC_PATH${NC}"
    echo "请先编译pycdc工具："
    echo "  git clone https://github.com/zrax/pycdc.git"
    echo "  cd pycdc"
    echo "  cmake CMakeLists.txt"
    echo "  make"
    exit 1
fi

# 创建输出目录
OUTPUT_DIR="decompiled_python"
mkdir -p "$OUTPUT_DIR"

echo -e "${GREEN}开始反编译项目中的.pyc文件...${NC}"
echo "输出目录: $OUTPUT_DIR"
echo ""

# 计数器
total_files=0
success_files=0
failed_files=0

# 查找并反编译所有.pyc文件
while IFS= read -r -d '' file; do
    total_files=$((total_files + 1))
    
    # 获取文件名（不含路径和扩展名）
    basename=$(basename "$file" .pyc)
    
    echo -e "${YELLOW}正在反编译: $file${NC}"
    
    # 反编译文件
    if "$PYCDC_PATH" "$file" > "$OUTPUT_DIR/${basename}.py" 2>/dev/null; then
        # 检查输出文件是否为空或只包含错误信息
        if [ -s "$OUTPUT_DIR/${basename}.py" ] && ! grep -q "WARNING: Decompyle incomplete" "$OUTPUT_DIR/${basename}.py" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ 成功: ${basename}.py${NC}"
            success_files=$((success_files + 1))
        else
            echo -e "${YELLOW}⚠ 部分成功: ${basename}.py (包含警告)${NC}"
            success_files=$((success_files + 1))
        fi
    else
        echo -e "${RED}✗ 失败: $file${NC}"
        failed_files=$((failed_files + 1))
        # 删除空的输出文件
        rm -f "$OUTPUT_DIR/${basename}.py"
    fi
    
done < <(find . -name "*.pyc" -type f -print0)

echo ""
echo -e "${GREEN}反编译完成！${NC}"
echo "总文件数: $total_files"
echo "成功: $success_files"
echo "失败: $failed_files"
echo ""
echo "反编译的文件保存在: $OUTPUT_DIR/"

# 显示反编译结果概览
if [ $success_files -gt 0 ]; then
    echo ""
    echo -e "${GREEN}反编译的文件列表:${NC}"
    ls -la "$OUTPUT_DIR/"
fi
